/**
 * @module node-opcua-address-space
 */
import { DataValue } from "node-opcua-data-value";
import { ReadRawModifiedDetails } from "node-opcua-service-history";
import { CallbackT } from "node-opcua-status-code";
import { IAddressSpace, IVariableHistorian, IVariableHistorianOptions, UAVariable } from "node-opcua-address-space-base";
import { UAVariableImpl } from "../ua_variable_impl";
export declare class VariableHistorian implements IVariableHistorian {
    readonly node: UAVariable;
    private readonly _timeline;
    private readonly _maxOnlineValues;
    private lastDate;
    private lastDatePicoSeconds;
    constructor(node: UAVariable, options: IVariableHistorianOptions);
    push(newDataValue: DataValue): Promise<void>;
    extractDataValues(historyReadRawModifiedDetails: ReadRawModifiedDetails, maxNumberToExtract: number, isReversed: boolean, reverseDataValue: boolean, callback: CallbackT<DataValue[]>): void;
}
/**
 */
export declare function AddressSpace_installHistoricalDataNode(this: IAddressSpace, node: UAVariableImpl, options?: IVariableHistorianOptions): void;
