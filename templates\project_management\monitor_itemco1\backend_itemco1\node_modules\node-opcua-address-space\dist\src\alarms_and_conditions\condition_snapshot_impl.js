"use strict";
var __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _ConditionSnapshotImpl_instances, _a, _ConditionSnapshotImpl__ensure_condition_values_correctness, _ConditionSnapshotImpl__visit, _ConditionSnapshotImpl__installOnChangeEventHandlers, _ConditionSnapshotImpl__record_condition_state, _ConditionSnapshotImpl__get_var, _ConditionSnapshotImpl__set_var, _ConditionSnapshotImpl__set_twoStateVariable, _ConditionSnapshotImpl__get_twoStateVariable;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConditionSnapshotImpl = void 0;
/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
const events_1 = require("events");
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_data_model_1 = require("node-opcua-data-model");
const node_opcua_debug_1 = require("node-opcua-debug");
const node_opcua_nodeid_1 = require("node-opcua-nodeid");
const node_opcua_status_code_1 = require("node-opcua-status-code");
const node_opcua_types_1 = require("node-opcua-types");
const node_opcua_variant_1 = require("node-opcua-variant");
const event_data_1 = require("../event_data");
const ua_two_state_variable_1 = require("../state_machine/ua_two_state_variable");
const ua_condition_impl_1 = require("./ua_condition_impl");
const debugLog = (0, node_opcua_debug_1.make_debugLog)(__filename);
const doDebug = (0, node_opcua_debug_1.checkDebugFlag)(__filename);
function normalizeName(str) {
    // return str.split(".").map(utils.lowerFirstLetter).join(".");
    return str;
}
const disabledVar = new node_opcua_variant_1.Variant({
    dataType: "StatusCode",
    value: node_opcua_status_code_1.StatusCodes.BadConditionDisabled
});
// list of Condition variables that should not be published as BadConditionDisabled when the condition
// is in a disabled state.
const _varTable = {
    BranchId: 1,
    ConditionClassId: 1,
    ConditionClassName: 1,
    ConditionName: 1,
    EnabledState: 1,
    "EnabledState.EffectiveDisplayName": 1,
    "EnabledState.Id": 1,
    "EnabledState.TransitionTime": 1,
    "EnabledState.EffectiveTransitionTime": 1,
    EventId: 1,
    EventType: 1,
    LocalTime: 1,
    SourceName: 1,
    SourceNode: 1,
    Time: 1
};
class ConditionSnapshotImpl extends events_1.EventEmitter {
    /**
     */
    constructor(condition, branchId) {
        super();
        _ConditionSnapshotImpl_instances.add(this);
        this.eventData = null;
        this.branchId = null;
        this._map = new Map();
        this._node_index = new Map();
        (0, node_opcua_assert_1.assert)(branchId instanceof node_opcua_nodeid_1.NodeId);
        // xx self.branchId = branchId;
        this.condition = condition;
        this.eventData = new event_data_1.EventData(condition);
        // a nodeId/Variant map
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__record_condition_state).call(this, condition);
        if ((0, node_opcua_nodeid_1.sameNodeId)(branchId, node_opcua_nodeid_1.NodeId.nullNodeId)) {
            __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__installOnChangeEventHandlers).call(this, condition, "");
        }
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "BranchId", node_opcua_variant_1.DataType.NodeId, branchId);
    }
    _constructEventData() {
        if (this.branchId && (0, node_opcua_nodeid_1.sameNodeId)(this.branchId, node_opcua_nodeid_1.NodeId.nullNodeId)) {
            __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__ensure_condition_values_correctness).call(this, this.condition, "", []);
        }
        const c = this.condition;
        const isDisabled = !c.getEnabledState();
        const eventData = new event_data_1.EventData(this.condition);
        for (const fullBrowsePath of this._map.keys()) {
            const node = this._node_index.get(fullBrowsePath);
            if (!node) {
                debugLog("cannot node for find key", fullBrowsePath);
                continue;
            }
            if (isDisabled && !Object.prototype.hasOwnProperty.call(_varTable, fullBrowsePath)) {
                eventData._createValue(fullBrowsePath, node, disabledVar);
            }
            else {
                eventData._createValue(fullBrowsePath, node, this._map.get(fullBrowsePath));
            }
        }
        return eventData;
    }
    /**
     *
     */
    getBranchId() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "BranchId");
    }
    /**
     *
     */
    getEventId() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "EventId");
    }
    /**
     *
     */
    getRetain() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "Retain");
    }
    /**
     *
     */
    setRetain(retainFlag) {
        retainFlag = !!retainFlag;
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "Retain", node_opcua_variant_1.DataType.Boolean, retainFlag);
    }
    /**
     *
     */
    renewEventId() {
        const addressSpace = this.condition.addressSpace;
        // create a new event  Id for this new condition
        const eventId = addressSpace.generateEventId();
        const ret = __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "EventId", node_opcua_variant_1.DataType.ByteString, eventId.value);
        return ret;
    }
    /**
     *
     */
    getEnabledState() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_twoStateVariable).call(this, "EnabledState");
    }
    /**
     *
     */
    setEnabledState(value, options) {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_twoStateVariable).call(this, "EnabledState", value, options);
    }
    /**
     *
     */
    getEnabledStateAsString() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "EnabledState").text;
    }
    /**
     *
     */
    getComment() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "Comment");
    }
    /**
     * Set condition comment
     *
     * Comment contains the last comment provided for a certain state (ConditionBranch). It may
     * have been provided by an AddComment Method, some other Method or in some other
     * manner. The initial value of this Variable is null, unless it is provided in some other manner. If
     * a Method provides as an option the ability to set a Comment, then the value of this Variable is
     * reset to null if an optional comment is not provided.
     *
     */
    setComment(txtMessage, options) {
        const txtMessage1 = (0, node_opcua_data_model_1.coerceLocalizedText)(txtMessage);
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "Comment", node_opcua_variant_1.DataType.LocalizedText, txtMessage1, options);
    }
    /**
     * set the condition message (localized text)
     */
    setMessage(txtMessage) {
        const txtMessage1 = (0, node_opcua_data_model_1.coerceLocalizedText)(txtMessage);
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "Message", node_opcua_variant_1.DataType.LocalizedText, txtMessage1);
    }
    /**
     *
     */
    setClientUserId(userIdentity) {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "ClientUserId", node_opcua_variant_1.DataType.String, userIdentity.toString());
    }
    /*
     *
     */
    /**
     * set the condition quality
     *
     *
     * as per spec 1.0.3 - Part 9
     *
     * Quality reveals the status of process values or other resources that this Condition
     * instance is based upon.
     *
     * If, for example, a process value is “Uncertain”, the associated “LevelAlarm”
     * Condition is also questionable.
     *
     * Values for the Quality can be any of the OPC StatusCodes defined in Part 8
     * as well as Good, Uncertain and Bad as defined in Part 4.
     *
     * These  StatusCodes are similar to but slightly more generic than the description
     * of data quality in the various field bus specifications.
     *
     * It is the responsibility of the Server to map internal status information to these codes.
     *
     * A Server which supports no quality information shall return Good.
     *
     * This quality can also reflect the communication status associated with the system that this
     * value or resource is based on and from which this Alarm was received. For communication
     * errors to the underlying system, especially those that result in some unavailable
     * Event fields, the quality shall be BadNoCommunication error.
     *
     * Quality refers to the quality of the data value(s) upon which this Condition is based.
     *
     * Since a Condition is usually based on one or more Variables, the Condition inherits
     *  the quality of these Variables. E.g., if the process value is “Uncertain”,
     *  the “LevelAlarm” Condition is also questionable.
     *
     * If more than one variable is represented by a given condition or if the condition
     * is from an underlining system and no direct mapping to a variable is available,
     * it is up to the application to determine what quality is displayed as part of the condition.
     */
    setQuality(quality, options) {
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "Quality", node_opcua_variant_1.DataType.StatusCode, quality, options);
    }
    /**
     *
     */
    getQuality() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "Quality");
    }
    /**
     * **as per spec 1.0.3 - Part 9**
     *
     * The Severity of a Condition is inherited from the base Event model defined in Part 5. It
     * indicates the urgency of the Condition and is also commonly called ‘priority’, especially in
     * relation to Alarms in the ProcessConditionClass.
     *
     * **as per spec 1.0.3 - Part 5**
     *
     * Severity is an indication of the urgency of the Event. This is also commonly
     * called “priority”.
     *
     * Values will range from 1 to 1 000, with 1 being the lowest severity and 1 000
     * being the highest.
     *
     * Typically, a severity of 1 would indicate an Event which is informational in nature,
     * while a value of 1 000 would indicate an Event of catastrophic nature, which could
     * potentially result in severe financial loss or loss of life.
     *
     * It is expected that very few Server implementations will support 1 000 distinct
     * severity levels.
     *
     * Therefore, Server developers are responsible for distributing their severity levels
     * across the  1 to 1 000 range in such a manner that clients can assume a linear
     * distribution.
     *
     *
     * For example, a  client wishing to present five severity levels to a user should be
     * able to do the following mapping:
     *
     *
     *            Client Severity OPC Severity
     *                HIGH        801 – 1 000
     *                MEDIUM HIGH 601 – 800
     *                MEDIUM      401 – 600
     *                MEDIUM LOW  201 – 400
     *                LOW           1 – 200
     *
     * In many cases a strict linear mapping of underlying source severities to the OPC
     * Severity range is not appropriate. The Server developer will instead intelligently
     * map the underlying source severities to the 1 to 1 000 OPC Severity range in some
     * other fashion.
     *
     * In particular, it it recommended that Server developers map Events of high urgency
     * into the OPC severity range of 667 to 1 000, Events of medium urgency into the
     * OPC severity range of 334 to 666 and Events of low urgency into OPC severities
     * of 1 to 333.
     */
    setSeverity(severity, options) {
        (0, node_opcua_assert_1.assert)(isFinite(severity), "expecting a UInt16");
        // record automatically last severity
        const lastSeverity = this.getSeverity();
        const sourceTimestamp = this.getSeveritySourceTimestamp();
        this.setLastSeverity(lastSeverity, { sourceTimestamp });
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "Severity", node_opcua_variant_1.DataType.UInt16, severity, options);
    }
    /**
     */
    getSeverity() {
        const c = this.condition;
        (0, node_opcua_assert_1.assert)(c.getEnabledState(), "condition must be enabled");
        const value = __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "Severity");
        return +value;
    }
    getSeveritySourceTimestamp() {
        const c = this.condition;
        return c.severity.readValue().sourceTimestamp || new Date();
    }
    /**
     *  LastSeverity provides the previous severity of the ConditionBranch.
     *
     * **as per spec 1.0.3 - part 9**
     *
     *
     *  Initially this Variable  contains a zero value;
     *  it will return a value only after a severity change. The new severity is
     *  supplied via the Severity Property which is inherited from the BaseEventType.
     *
     */
    setLastSeverity(severity, options) {
        severity = +severity;
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "LastSeverity", node_opcua_variant_1.DataType.UInt16, severity, options);
    }
    /**
     *
     */
    getLastSeverity() {
        const value = __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "LastSeverity");
        return +value;
    }
    /**
     * setReceiveTime
     *
     * **as per OPCUA 1.0.3 part 5**:
     *
     * ReceiveTime provides the time the OPC UA Server received the Event from the underlying
     * device of another Server.
     *
     * ReceiveTime is analogous to ServerTimestamp defined in Part 4, i.e.
     * in the case where the OPC UA Server gets an Event from another OPC UA Server, each Server
     * applies its own ReceiveTime. That implies that a Client may get the same Event, having the
     * same EventId, from different Servers having different values of the ReceiveTime.
     *
     * The ReceiveTime shall always be returned as value and the Server is not allowed to return a
     * StatusCode for the ReceiveTime indicating an error.
     *
     */
    setReceiveTime(time) {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "ReceiveTime", node_opcua_variant_1.DataType.DateTime, time, { sourceTimestamp: time || undefined });
    }
    /**
     * Time provides the time the Event occurred.
     *
     * **as per OPCUA 1.0.3 part 5**:
     *
     * This value is set as close to the event generator as
     * possible. It often comes from the underlying system or device.
     *
     * Once set, intermediate OPC UA Servers shall not alter the value.
     *
     */
    setTime(time) {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "Time", node_opcua_variant_1.DataType.DateTime, time, { sourceTimestamp: time });
    }
    /**
     * LocalTime is a structure containing the Offset and the DaylightSavingInOffset flag.
     *
     * The Offset specifies the time difference (in minutes) between the Time Property
     * and the time at the location in which the event was issued.
     *
     *
     * If DaylightSavingInOffset is TRUE, then Standard/Daylight savings time (DST) at
     * the originating location is in effect and Offset includes the DST correction.
     *
     * If `false` then the Offset does not include DST correction and DST may or may not have been
     * in effect.
     *
     */
    setLocalTime(localTime) {
        (0, node_opcua_assert_1.assert)(localTime instanceof node_opcua_types_1.TimeZoneDataType);
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_var).call(this, "LocalTime", node_opcua_variant_1.DataType.ExtensionObject, new node_opcua_types_1.TimeZoneDataType(localTime));
    }
    // read only !
    getSourceName() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "SourceName");
    }
    /**
     *
     */
    getSourceNode() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "SourceNode");
    }
    /**
     *
     */
    getEventType() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "EventType");
    }
    getMessage() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "Message");
    }
    isCurrentBranch() {
        return (0, node_opcua_nodeid_1.sameNodeId)(__classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_var).call(this, "BranchId"), node_opcua_nodeid_1.NodeId.nullNodeId);
    }
    // -- ACKNOWLEDGEABLE -------------------------------------------------------------------
    getAckedState() {
        const acknowledgeableCondition = this.condition;
        if (!acknowledgeableCondition.ackedState) {
            throw new Error("Node " +
                acknowledgeableCondition.browseName.toString() +
                " of type " +
                acknowledgeableCondition.typeDefinitionObj.browseName.toString() +
                " has no AckedState");
        }
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_twoStateVariable).call(this, "AckedState");
    }
    setAckedState(ackedState, options) {
        ackedState = !!ackedState;
        return this._setAckedState(ackedState, undefined, undefined, options);
    }
    getConfirmedState() {
        const acknowledgeableCondition = this.condition;
        (0, node_opcua_assert_1.assert)(acknowledgeableCondition.confirmedState, "Must have a confirmed state");
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_twoStateVariable).call(this, "ConfirmedState");
    }
    setConfirmedStateIfExists(confirmedState, options) {
        confirmedState = !!confirmedState;
        const acknowledgeableCondition = this.condition;
        if (!acknowledgeableCondition.confirmedState) {
            // no condition node has been defined (this is valid)
            // confirm state cannot be set
            return;
        }
        // todo deal with Error code BadConditionBranchAlreadyConfirmed
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_twoStateVariable).call(this, "ConfirmedState", confirmedState, options);
    }
    setConfirmedState(confirmedState) {
        const acknowledgeableCondition = this.condition;
        (0, node_opcua_assert_1.assert)(acknowledgeableCondition.confirmedState, "Must have a confirmed state.  Add ConfirmedState to the optionals");
        return this.setConfirmedStateIfExists(confirmedState);
    }
    // ---- Shelving
    getSuppressedState() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_twoStateVariable).call(this, "SuppressedState");
    }
    setSuppressedState(suppressed, options) {
        suppressed = !!suppressed;
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_twoStateVariable).call(this, "SuppressedState", suppressed, options);
    }
    getActiveState() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_twoStateVariable).call(this, "ActiveState");
    }
    setActiveState(newActiveState, options) {
        // xx var activeState = self.getActiveState();
        // xx if (activeState === newActiveState) {
        // xx     return StatusCodes.Bad;
        // xx }
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_twoStateVariable).call(this, "ActiveState", newActiveState, options);
        return node_opcua_status_code_1.StatusCodes.Good;
    }
    setLatchedState(newLatchedState, options) {
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_twoStateVariable).call(this, "LatchedState", newLatchedState, options);
        return node_opcua_status_code_1.StatusCodes.Good;
    }
    getLatchedState() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_twoStateVariable).call(this, "LatchedState");
    }
    setOutOfServiceState(newOutOfServiceState, options) {
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_twoStateVariable).call(this, "OutOfServiceState", newOutOfServiceState, options);
        return node_opcua_status_code_1.StatusCodes.Good;
    }
    getOutOfServiceState() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_twoStateVariable).call(this, "OutOfServiceState");
    }
    setSilentState(newSilentState, options) {
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_twoStateVariable).call(this, "SilentState", newSilentState, options);
        return node_opcua_status_code_1.StatusCodes.Good;
    }
    getSilentState() {
        return __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__get_twoStateVariable).call(this, "SilentState");
    }
    setShelvingState() {
        throw new Error("Method not implemented.");
    }
    toString() {
        //   public condition: any = null;
        //   public eventData: any = null;
        //   public branchId: NodeId | null = null;
        const t = this.condition.addressSpace.findNode(this.condition.typeDefinition);
        return ("" +
            "condition: " +
            (this.condition.browseName.toString() + " " + this.condition.nodeId.toString()) +
            ", type: " +
            (t.browseName.toString() + " " + t.nodeId.toString()) +
            ", branchId: " +
            (this.branchId ? this.branchId.toString() : "<null>") +
            ", acked: " +
            this.getAckedState() +
            ", confirmed: " +
            this.getConfirmedState() +
            ", activeState: " +
            this.getActiveState() +
            // + ", suppressed: " + this.getSuppressedState()
            ", retain: " +
            this.getRetain() +
            ", message: " +
            this.getMessage() +
            ", comment: " +
            this.getComment());
    }
    _setAckedState(requestedAckedState, conditionEventId, comment, options) {
        const ackedState = this.getAckedState();
        if (ackedState && requestedAckedState) {
            return node_opcua_status_code_1.StatusCodes.BadConditionBranchAlreadyAcked;
        }
        __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__set_twoStateVariable).call(this, "AckedState", requestedAckedState, options);
        return node_opcua_status_code_1.StatusCodes.Good;
    }
}
exports.ConditionSnapshotImpl = ConditionSnapshotImpl;
_a = ConditionSnapshotImpl, _ConditionSnapshotImpl_instances = new WeakSet(), _ConditionSnapshotImpl__ensure_condition_values_correctness = function _ConditionSnapshotImpl__ensure_condition_values_correctness(node, prefix, error) {
    const displayError = !!error;
    error = error || [];
    const aggregates = node.getAggregates();
    for (const aggregate of aggregates) {
        if (aggregate.nodeClass === node_opcua_data_model_1.NodeClass.Variable) {
            const name = aggregate.browseName.toString();
            const key = prefix + name;
            const snapshot_value = this._map.get(key).toString();
            const aggregateVariable = aggregate;
            const condition_value = aggregateVariable.readValue().value.toString();
            if (snapshot_value !== condition_value) {
                error.push(" Condition Branch0 is not in sync with node values for " +
                    key +
                    "\n v1= " +
                    snapshot_value +
                    "\n v2= " +
                    condition_value);
            }
            this._node_index.set(key, aggregateVariable);
            __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__ensure_condition_values_correctness).call(this, aggregate, prefix + name + ".", error);
        }
    }
    if (displayError && error.length) {
        throw new Error(error.join("\n"));
    }
}, _ConditionSnapshotImpl__visit = function _ConditionSnapshotImpl__visit(node, prefix) {
    const aggregates = node.getAggregates();
    for (const aggregate of aggregates) {
        if (aggregate.nodeClass === node_opcua_data_model_1.NodeClass.Variable) {
            const name = aggregate.browseName.toString();
            const key = prefix + name;
            // istanbul ignore next
            if (doDebug) {
                debugLog("adding key =", key);
            }
            const aggregateVariable = aggregate;
            this._map.set(key, aggregateVariable.readValue().value);
            this._node_index.set(key, aggregateVariable);
            __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__visit).call(this, aggregate, prefix + name + ".");
        }
    }
}, _ConditionSnapshotImpl__installOnChangeEventHandlers = function _ConditionSnapshotImpl__installOnChangeEventHandlers(node, prefix) {
    const aggregates = node.getAggregates();
    for (const aggregate of aggregates) {
        if (aggregate.nodeClass === node_opcua_data_model_1.NodeClass.Variable) {
            const name = aggregate.browseName.toString();
            const key = prefix + name;
            // istanbul ignore next
            if (doDebug) {
                debugLog("adding key =", key);
            }
            aggregate.on("value_changed", (newDataValue) => {
                this._map.set(key, newDataValue.value);
                this._node_index.set(key, aggregate);
            });
            __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__installOnChangeEventHandlers).call(this, aggregate, prefix + name + ".");
        }
    }
}, _ConditionSnapshotImpl__record_condition_state = function _ConditionSnapshotImpl__record_condition_state(condition) {
    this._map.clear();
    this._node_index.clear();
    (0, node_opcua_assert_1.assert)(condition instanceof ua_condition_impl_1.UAConditionImpl);
    __classPrivateFieldGet(this, _ConditionSnapshotImpl_instances, "m", _ConditionSnapshotImpl__visit).call(this, condition, "");
}, _ConditionSnapshotImpl__get_var = function _ConditionSnapshotImpl__get_var(varName) {
    const c = this.condition;
    if (!c.getEnabledState() && !Object.prototype.hasOwnProperty.call(_varTable, varName)) {
        // xx debuglog("ConditionSnapshot#_get_var condition enabled =", self.condition.getEnabledState());
        return disabledVar;
    }
    const key = normalizeName(varName);
    const variant = this._map.get(key);
    if (!variant) {
        throw new Error("cannot find key " + key);
    }
    return variant.value;
}, _ConditionSnapshotImpl__set_var = function _ConditionSnapshotImpl__set_var(varName, dataType, value, options) {
    const key = normalizeName(varName);
    // istanbul ignore next
    if (!this._map.has(key)) {
        // istanbul ignore next
        if (doDebug) {
            debugLog(" cannot find node " + varName);
            debugLog("  map=", [...this._map.keys()].join(" "));
        }
    }
    this._map.set(key, new node_opcua_variant_1.Variant({
        dataType,
        value
    }));
    const sourceTimestamp = options?.sourceTimestamp || new Date();
    const sourceTimestampKey = key + ".SourceTimestamp";
    if (this._map.has(sourceTimestampKey)) {
        // from spec 1.03 : 5.3 condition variables
        // a condition VariableType has a sourceTimeStamp exposed property
        // SourceTimestamp indicates the time of the last change of the Value of this ConditionVariable.
        // It shall be the same time that would be returned from the Read Service inside the DataValue
        // structure for the ConditionVariable Value Attribute.
        const variant = new node_opcua_variant_1.Variant({
            dataType: node_opcua_variant_1.DataType.DateTime,
            value: sourceTimestamp
        });
        this._map.set(sourceTimestampKey, variant);
        const node = this._node_index.get(sourceTimestampKey);
        this.emit("valueChanged", node, variant, { sourceTimestamp });
    }
    const variant = this._map.get(key);
    const node = this._node_index.get(key);
    if (!node) {
        // for instance localTime is optional
        debugLog("Cannot serVar " + varName + " dataType " + node_opcua_variant_1.DataType[dataType]);
        return;
    }
    (0, node_opcua_assert_1.assert)(node.nodeClass === node_opcua_data_model_1.NodeClass.Variable);
    this.emit("valueChanged", node, variant, { sourceTimestamp });
}, _ConditionSnapshotImpl__set_twoStateVariable = function _ConditionSnapshotImpl__set_twoStateVariable(varName, value, options) {
    value = !!value;
    const hrKey = _a.normalizeName(varName);
    const idKey = _a.normalizeName(varName + ".Id");
    const variant = new node_opcua_variant_1.Variant({ dataType: node_opcua_variant_1.DataType.Boolean, value });
    this._map.set(idKey, variant);
    // also change varName with human readable text
    const twoStateNode = this._node_index.get(hrKey);
    if (!twoStateNode) {
        throw new Error("Cannot find twoState Variable with name " + varName);
    }
    if (!(twoStateNode instanceof ua_two_state_variable_1.UATwoStateVariableImpl)) {
        throw new Error("Cannot find twoState Variable with name " + varName + " " + twoStateNode);
    }
    const hrValue = new node_opcua_variant_1.Variant({
        dataType: node_opcua_variant_1.DataType.LocalizedText,
        value: value ? twoStateNode.getTrueState() : twoStateNode.getFalseState()
    });
    this._map.set(hrKey, hrValue);
    const node = this._node_index.get(idKey);
    // also change ConditionNode if we are on currentBranch
    if (this.isCurrentBranch()) {
        (0, node_opcua_assert_1.assert)(twoStateNode instanceof ua_two_state_variable_1.UATwoStateVariableImpl);
        twoStateNode.setValue(value, options);
    }
    const sourceTimestamp = options?.effectiveTransitionTime || new Date();
    this.emit("valueChanged", node, variant, { sourceTimestamp });
}, _ConditionSnapshotImpl__get_twoStateVariable = function _ConditionSnapshotImpl__get_twoStateVariable(varName) {
    const key = _a.normalizeName(varName) + ".Id";
    const variant = this._map.get(key);
    // istanbul ignore next
    if (!variant) {
        return "???";
        // throw new Error("Cannot find TwoStateVariable with name " + varName);
    }
    return variant.value;
};
ConditionSnapshotImpl.normalizeName = normalizeName;
//# sourceMappingURL=condition_snapshot_impl.js.map