import { NodeId } from "node-opcua-nodeid";
import { VariantOptions } from "node-opcua-variant";
import { UAEventType } from "node-opcua-address-space-base";
import { NamespacePrivate } from "../namespace_private";
import { UANonExclusiveLimitAlarmEx } from "../../source/interfaces/alarms_and_conditions/ua_non_exclusive_limit_alarm_ex";
import { ConditionInfo } from "../../source/interfaces/alarms_and_conditions/condition_info_i";
import { InstantiateLimitAlarmOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_limit_alarm_options";
import { UALimitAlarmImpl } from "./ua_limit_alarm_impl";
export declare interface UANonExclusiveLimitAlarmImpl extends UANonExclusiveLimitAlarmEx, UALimitAlarmImpl {
    on(eventName: string, eventHandler: any): this;
    once(eventName: string, eventHandler: any): this;
}
export declare class UANonExclusiveLimitAlarmImpl extends UALimitAlarmImpl implements UANonExclusiveLimitAlarmEx {
    static instantiate(namespace: NamespacePrivate, type: UAEventType | NodeId | string, options: InstantiateLimitAlarmOptions, data?: Record<string, VariantOptions>): UANonExclusiveLimitAlarmImpl;
    _calculateConditionInfo(state: string | null, isActive: boolean, value: string, oldConditionInfo: ConditionInfo): ConditionInfo;
    _signalNewCondition2(states: {
        [key: string]: string;
    }, isActive: boolean, value: string): void;
    protected _setStateBasedOnInputValue(value: number): void;
}
