"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dumpToBSD = dumpToBSD;
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_types_1 = require("node-opcua-types");
const node_opcua_nodeid_1 = require("node-opcua-nodeid");
const construct_namespace_dependency_1 = require("./construct_namespace_dependency");
// tslint:disable-next-line: no-var-requires
const XMLWriter = require("xml-writer");
function dumpEnumeratedType(xw, e, name) {
    xw.startElement("opc:EnumeratedType");
    xw.writeAttribute("Name", name);
    xw.writeAttribute("LengthInBits", "32");
    for (const f of e.fields || []) {
        xw.startElement("opc:EnumeratedValue");
        xw.writeAttribute("Name", f.name);
        (0, node_opcua_assert_1.assert)(f.value[0] === 0, "unsupported 64 bit value !");
        xw.writeAttribute("Value", f.value[1].toString());
        xw.endElement();
    }
    xw.endElement();
}
function buildXmlName(addressSpace, map, nodeId) {
    if (node_opcua_nodeid_1.NodeId.sameNodeId(nodeId, node_opcua_nodeid_1.NodeId.nullNodeId)) {
        return "ua:ExtensionObject";
    }
    const node = addressSpace.findNode(nodeId);
    // istanbul ignore next
    if (!node) {
        throw new Error("Cannot find Node for" + nodeId?.toString());
    }
    const typeName = node.browseName.name;
    const n = node.nodeId;
    const prefix = (n.identifierType === node_opcua_nodeid_1.NodeIdType.NUMERIC && n.namespace === 0) ? (n.value <= 15 ? "opc" : "ua") : map.get(node.nodeId.namespace);
    return prefix + ":" + (typeName === "Structure" && prefix === "ua" ? "ExtensionObject" : typeName);
}
// eslint-disable-next-line max-statements
function dumpDataTypeStructure(xw, addressSpace, map, structureDefinition, structureDefinitionBase, name, doc) {
    xw.startElement("opc:StructuredType");
    xw.writeAttribute("Name", name);
    xw.writeAttribute("BaseType", buildXmlName(addressSpace, map, structureDefinition.baseDataType));
    if (doc) {
        xw.startElement("opc:Documentation");
        xw.text(doc);
        xw.endElement();
    }
    const fields = structureDefinition.fields || [];
    // get base class
    const nbFieldsInBase = structureDefinitionBase ? structureDefinitionBase.fields?.length || 0 : 0;
    let optionalsCount = 0;
    for (let index = nbFieldsInBase; index < fields.length; index++) {
        const f = fields[index];
        if (f.isOptional) {
            xw.startElement("opc:Field");
            xw.writeAttribute("Name", f.name + "Specified");
            xw.writeAttribute("TypeName", "opc:Bit");
            xw.endElement();
            optionalsCount++;
        }
    }
    // istanbul ignore next
    if (optionalsCount >= 32) {
        throw new Error("Too many optionals fields");
    }
    if (optionalsCount) {
        /*
                const padding = optionalsCount <= 8
                    ? (8 - optionalsCount)
                    : (optionalsCount <= 16)
                        ? (16 - optionalsCount)
                        : (32 - optionalsCount)
                    ;
        */
        const padding = 32 - optionalsCount;
        if (padding !== 0) {
            xw.startElement("opc:Field");
            xw.writeAttribute("Name", "Reserved1");
            xw.writeAttribute("TypeName", "opc:Bit");
            xw.writeAttribute("Length", padding.toString());
            xw.endElement();
        }
    }
    for (let index = nbFieldsInBase; index < fields.length; index++) {
        const f = fields[index];
        const isArray = f.valueRank > 0 && f.arrayDimensions?.length;
        if (isArray) {
            xw.startElement("opc:Field");
            xw.writeAttribute("Name", "NoOf" + f.name);
            xw.writeAttribute("TypeName", "opc:Int32");
            if (f.isOptional) {
                xw.writeAttribute("SwitchField", f.name + "Specified");
            }
            xw.endElement();
        }
        xw.startElement("opc:Field");
        xw.writeAttribute("Name", f.name);
        const typeName = buildXmlName(addressSpace, map, f.dataType);
        xw.writeAttribute("TypeName", typeName);
        if (isArray) {
            xw.writeAttribute("LengthField", "NoOf" + f.name);
        }
        if (f.isOptional) {
            xw.writeAttribute("SwitchField", f.name + "Specified");
        }
        xw.endElement();
    }
    xw.endElement();
}
function dumpDataTypeToBSD(xw, dataType, map) {
    const addressSpace = dataType.addressSpace;
    const name = dataType.browseName.name;
    const definition = dataType.getDefinition();
    if (definition instanceof node_opcua_types_1.StructureDefinition) {
        const structureDefinitionBase = dataType.subtypeOfObj?.getStructureDefinition();
        dumpDataTypeStructure(xw, addressSpace, map, definition, structureDefinitionBase, name);
    }
    if (definition instanceof node_opcua_types_1.EnumDefinition) {
        dumpEnumeratedType(xw, definition, name);
    }
}
function shortcut(namespace) {
    return "n" + namespace.index;
}
function dumpToBSD(namespace) {
    const dependency = (0, construct_namespace_dependency_1.constructNamespaceDependency)(namespace);
    const addressSpace = namespace.addressSpace;
    const xw = new XMLWriter(true);
    //xx xw.startDocument():// { encoding: "utf-8", version: "1.0" });
    xw.startElement("opc:TypeDictionary");
    xw.writeAttribute("xmlns:opc", "http://opcfoundation.org/BinarySchema/");
    xw.writeAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
    xw.writeAttribute("xmlns:ua", "http://opcfoundation.org/UA/");
    xw.writeAttribute("xmlns:tns", namespace.namespaceUri);
    const map = new Map();
    map.set(namespace.index, "tns");
    for (const dependantNamespace of dependency) {
        const namespaceIndex = dependantNamespace.index;
        if (namespaceIndex === 0 || namespaceIndex === namespace.index) {
            continue;
        }
        const ns = shortcut(dependantNamespace);
        map.set(namespaceIndex, ns);
        xw.writeAttribute(`xmlns:${ns}`, dependantNamespace.namespaceUri);
    }
    xw.writeAttribute("DefaultByteOrder", "LittleEndian");
    xw.writeAttribute("TargetNamespace", namespace.namespaceUri);
    // <opc:Import Namespace="http://opcfoundation.org/UA/"/>
    for (const dependantNamespace of dependency) {
        if (dependantNamespace.index === namespace.index) {
            continue;
        }
        xw.startElement("opc:Import").writeAttribute("Namespace", dependantNamespace.namespaceUri).endElement();
    }
    //
    for (const dataType of namespace._dataTypeIterator()) {
        dumpDataTypeToBSD(xw, dataType, map);
    }
    xw.endElement();
    //    xw.endDocument();
    return xw.toString();
}
//# sourceMappingURL=dump_to_bsd.js.map