{"version": 3, "file": "dump_to_bsd.js", "sourceRoot": "", "sources": ["../../../src/nodeset_tools/dump_to_bsd.ts"], "names": [], "mappings": ";;AAqJA,8BAgDC;AApMD,yDAA2C;AAC3C,uDAAuE;AACvE,yDAAgE;AAIhE,qFAAgF;AAEhF,4CAA4C;AAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAExC,SAAS,kBAAkB,CAAC,EAAa,EAAE,CAAiB,EAAE,IAAY;IACtE,EAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IACtC,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAChC,EAAE,CAAC,cAAc,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACxC,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;QAC7B,EAAE,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;QACvC,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,IAAK,CAAC,CAAC;QACnC,IAAA,0BAAM,EAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,4BAA4B,CAAC,CAAC;QACvD,EAAE,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClD,EAAE,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IACD,EAAE,CAAC,UAAU,EAAE,CAAC;AACpB,CAAC;AACD,SAAS,YAAY,CAAC,YAAiC,EAAE,GAAuB,EAAE,MAAc;IAC5F,IAAI,0BAAM,CAAC,UAAU,CAAC,MAAM,EAAE,0BAAM,CAAC,UAAU,CAAC,EAAE,CAAC;QAC/C,OAAO,oBAAoB,CAAC;IAChC,CAAC;IACD,MAAM,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC3C,uBAAuB;IACvB,IAAI,CAAC,IAAI,EAAE,CAAC;QACR,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IACjE,CAAC;IACD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAK,CAAC;IAEvC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAiB,CAAC;IACjC,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,cAAc,KAAK,8BAAU,CAAC,OAAO,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAChJ,OAAO,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvG,CAAC;AAED,0CAA0C;AAC1C,SAAS,qBAAqB,CAC1B,EAAa,EACb,YAA2B,EAC3B,GAAuB,EACvB,mBAAwC,EACxC,uBAA+D,EAC/D,IAAY,EACZ,GAAY;IAEZ,EAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IACtC,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAChC,EAAE,CAAC,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC,YAAmC,EAAE,GAAG,EAAE,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC;IAExH,IAAI,GAAG,EAAE,CAAC;QACN,EAAE,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QACrC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACb,EAAE,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,IAAI,EAAE,CAAC;IAChD,iBAAiB;IACjB,MAAM,cAAc,GAAG,uBAAuB,CAAC,CAAC,CAAC,uBAAuB,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjG,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,KAAK,IAAI,KAAK,GAAG,cAAc,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;QAC9D,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;YACf,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC7B,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;YAChD,EAAE,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACzC,EAAE,CAAC,UAAU,EAAE,CAAC;YAChB,cAAc,EAAE,CAAC;QACrB,CAAC;IACL,CAAC;IAED,uBAAuB;IACvB,IAAI,cAAc,IAAI,EAAE,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,cAAc,EAAE,CAAC;QACjB;;;;;;;UAOE;QACF,MAAM,OAAO,GAAG,EAAE,GAAG,cAAc,CAAC;QACpC,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAChB,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC7B,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACvC,EAAE,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACzC,EAAE,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAChD,EAAE,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;IACL,CAAC;IACD,KAAK,IAAI,KAAK,GAAG,cAAc,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;QAC9D,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAExB,MAAM,OAAO,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,MAAM,CAAC;QAE7D,IAAI,OAAO,EAAE,CAAC;YACV,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC7B,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC;YAC5C,EAAE,CAAC,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;gBACf,EAAE,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;YAC3D,CAAC;YACD,EAAE,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;QAED,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAC7B,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,IAAK,CAAC,CAAC;QAEnC,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAmC,EAAE,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;QACpF,EAAE,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACxC,IAAI,OAAO,EAAE,CAAC;YACV,EAAE,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,GAAG,CAAC,CAAC,IAAK,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;YACf,EAAE,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;QAC3D,CAAC;QACD,EAAE,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IACD,EAAE,CAAC,UAAU,EAAE,CAAC;AACpB,CAAC;AAED,SAAS,iBAAiB,CAAC,EAAa,EAAE,QAAoB,EAAE,GAAuB;IACnF,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;IAE3C,MAAM,IAAI,GAAW,QAAQ,CAAC,UAAU,CAAC,IAAK,CAAC;IAE/C,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;IAC5C,IAAI,UAAU,YAAY,sCAAmB,EAAE,CAAC;QAC5C,MAAM,uBAAuB,GAAG,QAAQ,CAAC,YAAY,EAAE,sBAAsB,EAAE,CAAC;QAChF,qBAAqB,CAAC,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,uBAAuB,EAAE,IAAI,CAAC,CAAC;IAC5F,CAAC;IACD,IAAI,UAAU,YAAY,iCAAc,EAAE,CAAC;QACvC,kBAAkB,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;AACL,CAAC;AAED,SAAS,QAAQ,CAAC,SAAqB;IACnC,OAAO,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC;AACjC,CAAC;AACD,SAAgB,SAAS,CAAC,SAA2B;IACjD,MAAM,UAAU,GAAiB,IAAA,6DAA4B,EAAC,SAAS,CAAC,CAAC;IAEzE,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;IAE5C,MAAM,EAAE,GAAc,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;IAE1C,kEAAkE;IAElE,EAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IAEtC,EAAE,CAAC,cAAc,CAAC,WAAW,EAAE,wCAAwC,CAAC,CAAC;IACzE,EAAE,CAAC,cAAc,CAAC,WAAW,EAAE,2CAA2C,CAAC,CAAC;IAC5E,EAAE,CAAC,cAAc,CAAC,UAAU,EAAE,8BAA8B,CAAC,CAAC;IAC9D,EAAE,CAAC,cAAc,CAAC,WAAW,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;IAEvD,MAAM,GAAG,GAAuB,IAAI,GAAG,EAAE,CAAC;IAE1C,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAC,KAAK,CAAC,CAAC;IAE/B,KAAK,MAAM,kBAAkB,IAAI,UAAU,EAAE,CAAC;QAC1C,MAAM,cAAc,GAAG,kBAAkB,CAAC,KAAK,CAAC;QAChD,IAAI,cAAc,KAAK,CAAC,IAAI,cAAc,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;YAC7D,SAAS;QACb,CAAC;QACD,MAAM,EAAE,GAAG,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QACxC,GAAG,CAAC,GAAG,CAAC,cAAc,EAAC,EAAE,CAAC,CAAC;QAC3B,EAAE,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC;IACtE,CAAC;IAED,EAAE,CAAC,cAAc,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;IACtD,EAAE,CAAC,cAAc,CAAC,iBAAiB,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;IAE7D,yDAAyD;IACzD,KAAK,MAAM,kBAAkB,IAAI,UAAU,EAAE,CAAC;QAC1C,IAAI,kBAAkB,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;YAC/C,SAAS;QACb,CAAC;QACD,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,WAAW,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE,CAAC;IAC5G,CAAC;IACD,EAAE;IACF,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,iBAAiB,EAAE,EAAE,CAAC;QACnD,iBAAiB,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IACD,EAAE,CAAC,UAAU,EAAE,CAAC;IAChB,uBAAuB;IAEvB,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC;AACzB,CAAC"}