import { DataValue } from "node-opcua-data-value";
import { NodeId } from "node-opcua-nodeid";
import { DataType, VariantOptions } from "node-opcua-variant";
import { UAVariableT } from "node-opcua-address-space-base";
import { AddressSpace } from "../address_space";
import { NamespacePrivate } from "../namespace_private";
import { UAExclusiveDeviationAlarmEx } from "../../source/interfaces/alarms_and_conditions/ua_exclusive_deviation_alarm_ex";
import { InstantiateExclusiveLimitAlarmOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_exclusive_limit_alarm_options";
import { InstallSetPointOptions, SetPointSupport } from "../../source/interfaces/alarms_and_conditions/install_setpoint_options";
import { UAExclusiveLimitAlarmImpl } from "./ua_exclusive_limit_alarm_impl";
export declare interface UAExclusiveDeviationAlarmImpl extends UAExclusiveDeviationAlarmEx, UAExclusiveLimitAlarmImpl {
    on(eventName: string, eventHandler: any): this;
    once(eventName: string, eventHandler: any): this;
    get addressSpace(): AddressSpace;
}
export declare class UAExclusiveDeviationAlarmImpl extends UAExclusiveLimitAlarmImpl implements UAExclusiveDeviationAlarmEx {
    static instantiate(namespace: NamespacePrivate, type: string | NodeId, options: InstantiateExclusiveLimitAlarmOptions, data?: Record<string, VariantOptions>): UAExclusiveDeviationAlarmImpl;
    getSetpointNodeNode(): UAVariableT<number, DataType.Double> | UAVariableT<number, DataType.Float> | undefined;
    getSetpointValue(): number | null;
    _onSetpointDataValueChange(dataValue: DataValue): void;
    _install_setpoint(options: InstallSetPointOptions): any;
    _setStateBasedOnInputValue(value: number): void;
}
export interface UAExclusiveDeviationAlarmHelper extends SetPointSupport {
}
