import { Base<PERSON>ode, UAMethod, UAObject, UAVariable, CloneFilter } from "node-opcua-address-space-base";
export type OptionalMap = Record<string, string | Record<string, any>>;
export declare class MandatoryChildOrRequestedOptionalFilter implements CloneFilter {
    private readonly instance;
    private readonly optionalsMap;
    private readonly references;
    private readonly copyAlsoAllOptionals;
    constructor(instance: BaseNode, copyAlsoAllOptionals: boolean, optionalsMap: OptionalMap);
    shouldKeep(node: BaseNode): boolean;
    filterFor(childInstance: UAVariable | UAObject | UAMethod): CloneFilter;
}
