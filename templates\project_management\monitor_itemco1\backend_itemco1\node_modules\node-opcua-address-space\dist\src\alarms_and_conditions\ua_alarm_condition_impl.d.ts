import { DataValue } from "node-opcua-data-value";
import { NodeId } from "node-opcua-nodeid";
import { VariantOptions } from "node-opcua-variant";
import { BaseNode, INamespace, UAEventType, UAVariable } from "node-opcua-address-space-base";
import { ConditionInfo } from "../../source/interfaces/alarms_and_conditions/condition_info_i";
import { UAAlarmConditionEx } from "../../source/interfaces/alarms_and_conditions/ua_alarm_condition_ex";
import { InstantiateAlarmConditionOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_alarm_condition_options";
import { UAAcknowledgeableConditionImpl } from "./ua_acknowledgeable_condition_impl";
export declare interface UAAlarmConditionImpl extends UAAlarmConditionEx, UAAcknowledgeableConditionImpl {
    on(eventName: string, eventHandler: any): this;
}
export declare class UAAlarmConditionImpl extends UAAcknowledgeableConditionImpl implements UAAlarmConditionEx {
    static MaxDuration: number;
    static instantiate(namespace: INamespace, alarmConditionTypeId: UAEventType | string | NodeId, options: InstantiateAlarmConditionOptions, data?: Record<string, VariantOptions>): UAAlarmConditionImpl;
    dispose(): void;
    activateAlarm(): void;
    deactivateAlarm(retain?: boolean): void;
    /**
     * @deprecated use deactivateAlarm instead (with no s after de-activate)
     */
    protected desactivateAlarm(): void;
    isSuppressedOrShelved(): boolean;
    getSuppressedOrShelved(): boolean;
    /**
     *
     * note: duration must be greater than 10ms and lesser than 2**31 ms
     */
    setMaxTimeShelved(duration: number): void;
    /**
     * note: return a  Duration
     */
    getMaxTimeShelved(): number;
    /**

     * @return {BaseNode} return the node in the address space pointed by the inputNode value
     *
     * Note: please note the difference between alarm.inputNode
     *    *  alarm.inputNode is a UAVariable property of the alarm object holding the nodeid of the input
     *       node in its value.
     *    *  getInputNodeNode() is the UAVariable that contains the value that affects the state of the alarm and
     *       whose node id is stored in alarm.inputNode
     */
    getInputNodeNode(): UAVariable | null;
    /**
     *
     */
    getInputNodeValue(): any | null;
    updateState(): void;
    protected _onInputDataValueChange(newValue: DataValue): void;
    /**
     * install mechanism that listen to input node datavalue changes so that alarm status
     * can be automatically updated appropriately.
     * @param inputNode {BaseNode}
     * @return {void}
     * @protected
     */
    installInputNodeMonitoring(inputNode: BaseNode | NodeId): void;
    getCurrentConditionInfo(): ConditionInfo;
    /***
     *
     * this method need to be overridden by the instantiate to allow custom message and severity
     * to be set based on specific context of the alarm.
     *
     * @example
     *
     *
     *    var myAlarm = addressSpace.instantiateExclusiveLimitAlarm({...});
     *    myAlarm._calculateConditionInfo = function(stateName,value,oldCondition) {
     *       var percent = Math.ceil(value * 100);
     *       return new ConditionInfo({
     *            message: "Tank is almost " + percent + "% full",
     *            severity: 100,
     *            quality: StatusCodes.Good
     *      });
     *    };
     *
     */
    _calculateConditionInfo(stateData: string | null, isActive: boolean, value: string, oldCondition: ConditionInfo): ConditionInfo;
    _signalInitialCondition(): void;
    _signalNewCondition(stateName: string | null, isActive?: boolean, value?: string): void;
}
