/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
import { INamespace, UAVariableT } from "node-opcua-address-space-base";
import { DataValue } from "node-opcua-data-value";
import { NodeId, NodeIdLike } from "node-opcua-nodeid";
import { UAOffNormalAlarm_Base } from "node-opcua-nodeset-ua";
import { DataType, VariantOptions } from "node-opcua-variant";
import { InstantiateOffNormalAlarmOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_off_normal_alarm_options";
import { UADiscreteAlarmEx } from "../../source/interfaces/alarms_and_conditions/ua_discrete_alarm_ex";
import { UADiscreteAlarmImpl } from "./ua_discrete_alarm_impl";
export declare interface UAOffNormalAlarmEx extends Omit<UAOffNormalAlarm_Base, "ackedState" | "activeState" | "confirmedState" | "enabledState" | "latchedState" | "outOfServiceState" | "shelvingState" | "silenceState" | "suppressedState">, UADiscreteAlarmEx {
    getNormalStateNode(): UAVariableT<NodeId, DataType.NodeId> | null;
    getNormalStateValue(): NodeId | null;
    setNormalStateValue(value: NodeIdLike): void;
}
export declare interface UAOffNormalAlarmImpl extends UAOffNormalAlarmEx, UADiscreteAlarmImpl {
    on(eventName: string, eventHandler: any): this;
    once(eventName: string, eventHandler: any): this;
}
/**
 * The OffNormalAlarmType is a specialization of the DiscreteAlarmType intended to represent a
 * discrete Condition that is considered to be not normal.
 * This sub type is usually used to indicate that a discrete value is in an Alarm state, it is active as
 * long as a non-normal value is present.
 */
export declare class UAOffNormalAlarmImpl extends UADiscreteAlarmImpl implements UAOffNormalAlarmEx {
    /**
     * When the value of inputNode doesn't match the normalState node value, then the alarm is raised.
     *
     */
    static instantiate<T, DT extends DataType>(namespace: INamespace, limitAlarmTypeId: string | NodeId, options: InstantiateOffNormalAlarmOptions, data?: Record<string, VariantOptions>): UAOffNormalAlarmImpl;
    getNormalStateNode(): UAVariableT<NodeId, DataType.NodeId> | null;
    /**
     */
    getNormalStateValue(): NodeId | null;
    /**
     */
    setNormalStateValue(value: NodeIdLike): void;
    updateAlarmState(isActive: boolean, message: string): void;
    private _mayBe_updateAlarmState;
    protected _onInputDataValueChange(dataValue: DataValue): void;
    protected _onNormalStateDataValueChange(dataValue: DataValue): void;
}
