<?php
if(!defined('SOURCES')) die("Error");

// Include database configuration
require_once 'config/database.php';

// Đ<PERSON>m bảo admin tồn tại trong database
ensureAdminExists();

/* Kiểm tra đăng nhập và session timeout */
function checkProjectAuth() {
    // Kiểm tra session timeout (30 phút)
    if(isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
        session_unset();
        session_destroy();
        return false;
    }

    // Cập nhật last activity
    $_SESSION['last_activity'] = time();

    return isset($_SESSION['project_auth']) && $_SESSION['project_auth'] === true;
}

/* Đảm bảo admin có record trong database */
function ensureAdminExists() {
    global $pdo;

    try {
        // Kiểm tra admin đã tồn tại chưa
        $stmt = $pdo->prepare("SELECT * FROM project_users WHERE username = 'Technojet' AND user_type = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();

        if(!$admin) {
            // Tạo admin nếu chưa tồn tại (password NULL = chưa đổi, dùng mặc định)
            $stmt = $pdo->prepare("INSERT INTO project_users (username, password, email, user_type, status, created_at, updated_at)
                                  VALUES ('Technojet', NULL, '<EMAIL>', 'admin', 'active', NOW(), NOW())");
            $stmt->execute();
        }
    } catch(Exception $e) {
        // Admin creation failed - continue silently
    }
}

/* ===== HELPER FUNCTIONS ===== */

/* Hàm xóa thư mục và tất cả nội dung bên trong */
function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }
    return rmdir($dir);
}

/* Redirect với thông báo */
function redirectWithMessage($url, $message, $type = 'success') {
    $param = $type === 'success' ? 'success' : 'error';
    header("Location: $url?$param=" . urlencode($message));
    exit;
}

/* Kiểm tra quyền admin */
function requireAdmin() {
    if($_SESSION['user_type'] !== 'admin') {
        redirectWithMessage('quan-ly-du-an', 'Không có quyền thực hiện', 'error');
    }
}



/* Xử lý đăng nhập */
if(isset($_POST['login_project'])) {
    $username = isset($_POST['company_name']) ? trim($_POST['company_name']) : '';
    $password = isset($_POST['password']) ? trim($_POST['password']) : '';

    if(empty($username)) {
        $login_error = "Vui lòng nhập tên công ty!";
    } else {
        // Kiểm tra user trong database
        $user = getUserByUsername($username);

        if(!$user) {
            $login_error = "Tên công ty không tồn tại trong hệ thống!";
        } else {
            // Kiểm tra password
            if(empty($password)) {
                $login_error = "Vui lòng nhập mật khẩu!";
            } else {
                // Kiểm tra xem customer đã có password chưa
                if($user['user_type'] === 'customer' && (empty($user['password']) || is_null($user['password']))) {
                    $login_error = "Tài khoản chưa được kích hoạt. Vui lòng liên hệ admin để đặt mật khẩu.";
                } else {
                    // Kiểm tra password
                    $password_match = false;
                    if($user['user_type'] === 'admin' && $username === 'Technojet') {
                        // Admin: nếu đã đổi mật khẩu thì chỉ dùng mật khẩu mới, chưa đổi thì dùng mặc định
                        if($user['password'] !== null && $user['password'] !== '') {
                            // Đã đổi mật khẩu - chỉ chấp nhận mật khẩu mới
                            if($user['password'] === $password) {
                                $password_match = true;
                            }
                        } else {
                            // Chưa đổi mật khẩu (password NULL) - chỉ chấp nhận mật khẩu mặc định
                            if($password === 'Technojet1') {
                                $password_match = true;
                            }
                        }
                    } elseif($user['user_type'] === 'customer' && $user['password'] === $password) {
                        $password_match = true;
                    }

                    if($password_match) {
                        // Đăng nhập thành công
                        $_SESSION['project_auth'] = true;
                        $_SESSION['user_type'] = $user['user_type'];
                        $_SESSION['company_name'] = $user['username'];
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['last_activity'] = time();

                        // Nếu là customer, lấy thông tin company và projects
                        if($user['user_type'] === 'customer') {
                            $_SESSION['company_id'] = $user['company_id'];
                            $projects = getCompanyProjects($user['company_id']);
                            $_SESSION['company_projects'] = $projects;
                        }

                        header('Location: quan-ly-du-an');
                        exit;
                    } else {
                        $login_error = "Mật khẩu không chính xác!";
                    }
                }
            }
        }
    }
}

/* Xử lý quên mật khẩu */
if(isset($_POST['forgot_password'])) {
    $company_name = isset($_POST['company_name_reset']) ? trim($_POST['company_name_reset']) : '';

    if(empty($company_name)) {
        $login_error = "Vui lòng nhập tên công ty!";
    } else {
        // Kiểm tra user trong database
        $user = getUserByUsername($company_name);

        if(!$user || $user['user_type'] !== 'customer') {
            $login_error = "Tên công ty không tồn tại trong hệ thống!";
        } else {
            // Generate reset token
            $token = generatePasswordResetToken($company_name);

            if($token) {
                // Send reset email
                $email_sent = sendPasswordResetEmail($company_name, $user['email'], $token);

                if($email_sent) {
                    $login_success = "Email đặt lại mật khẩu đã được gửi đến địa chỉ email của bạn. Vui lòng kiểm tra hộp thư và làm theo hướng dẫn.";
                } else {
                    $login_error = "Có lỗi xảy ra khi gửi email. Vui lòng thử lại sau hoặc liên hệ admin.";
                }
            } else {
                $login_error = "Có lỗi xảy ra. Vui lòng thử lại sau.";
            }
        }
    }
}

/* Xử lý đặt lại mật khẩu */
if(isset($_POST['reset_password_submit'])) {
    $token = isset($_POST['reset_token']) ? trim($_POST['reset_token']) : '';
    $company_name = isset($_POST['company_name']) ? trim($_POST['company_name']) : '';
    $new_password = isset($_POST['new_password']) ? trim($_POST['new_password']) : '';
    $confirm_password = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';

    if(empty($token) || empty($company_name)) {
        $reset_error = "Thông tin không hợp lệ!";
    } elseif(empty($new_password)) {
        $reset_error = "Vui lòng nhập mật khẩu mới!";
    } elseif(strlen($new_password) < 6) {
        $reset_error = "Mật khẩu mới phải có ít nhất 6 ký tự!";
    } elseif($new_password !== $confirm_password) {
        $reset_error = "Mật khẩu xác nhận không khớp!";
    } else {
        // Verify token and reset password
        $reset_success_result = resetPasswordWithToken($token, $company_name, $new_password);

        if($reset_success_result) {
            $reset_success = "Đặt lại mật khẩu thành công! Bạn có thể đăng nhập với mật khẩu mới.";
        } else {
            $reset_error = "Token không hợp lệ hoặc đã hết hạn. Vui lòng yêu cầu đặt lại mật khẩu mới.";
        }
    }
}


/* Xử lý thêm khách hàng */
if(isset($_POST['add_company_submit'])) {
    $company_name = trim($_POST['company_name']);
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    $address = trim($_POST['address']);

    // Validation
    if(empty($company_name) || empty($full_name) || empty($email) || empty($username) || empty($password)) {
        header('Location: quan-ly-du-an?action=add_company&error=' . urlencode('Vui lòng điền đầy đủ thông tin bắt buộc'));
        exit;
    }

    if(strlen($password) < 6) {
        header('Location: quan-ly-du-an?action=add_company&error=' . urlencode('Mật khẩu phải có ít nhất 6 ký tự'));
        exit;
    }

    if(checkUserExists($username, $email)) {
        header('Location: quan-ly-du-an?action=add_company&error=' . urlencode('Tên đăng nhập hoặc email đã tồn tại'));
        exit;
    }

    $data = [
        'company_name' => $company_name,
        'full_name' => $full_name,
        'email' => $email,
        'phone' => $phone,
        'username' => $username,
        'password' => $password,
        'address' => $address
    ];

    if(addNewCompany($data)) {
        header('Location: quan-ly-du-an?action=add_company&success=1');
        exit;
    } else {
        header('Location: quan-ly-du-an?action=add_company&error=' . urlencode('Có lỗi xảy ra khi thêm khách hàng'));
        exit;
    }
}

/* Xử lý đổi mật khẩu */
if(isset($_POST['change_password_submit'])) {
    $current_password = isset($_POST['current_password']) ? trim($_POST['current_password']) : '';
    $new_password = trim($_POST['new_password']);
    $confirm_password = trim($_POST['confirm_password']);
    $is_first_time = isset($_GET['first_time']) || isset($_SESSION['first_time_login']);

    if($new_password !== $confirm_password) {
        $redirect_url = 'quan-ly-du-an?action=change_password&error=' . urlencode('Mật khẩu xác nhận không khớp');
        if($is_first_time) $redirect_url .= '&first_time=1';
        header('Location: ' . $redirect_url);
        exit;
    }

    if(strlen($new_password) < 6) {
        $redirect_url = 'quan-ly-du-an?action=change_password&error=' . urlencode('Mật khẩu mới phải có ít nhất 6 ký tự');
        if($is_first_time) $redirect_url .= '&first_time=1';
        header('Location: ' . $redirect_url);
        exit;
    }

    // Lấy thông tin user từ database
    $user = getUserByUsername($_SESSION['company_name']);
    if(!$user) {
        $redirect_url = 'quan-ly-du-an?action=change_password&error=' . urlencode('Không tìm thấy thông tin tài khoản');
        if($is_first_time) $redirect_url .= '&first_time=1';
        header('Location: ' . $redirect_url);
        exit;
    }

    // Kiểm tra mật khẩu hiện tại (nếu không phải first time)
    if(!$is_first_time) {
        $password_correct = false;

        // Kiểm tra theo loại user
        if($user['user_type'] === 'admin' && $user['username'] === 'Technojet') {
            // Admin: nếu đã đổi mật khẩu thì chỉ dùng mật khẩu mới, chưa đổi thì dùng mặc định
            if($user['password'] !== null && $user['password'] !== '') {
                // Đã đổi mật khẩu - chỉ chấp nhận mật khẩu mới
                if($user['password'] === $current_password) {
                    $password_correct = true;
                }
            } else {
                // Chưa đổi mật khẩu (password NULL) - chỉ chấp nhận mật khẩu mặc định
                if($current_password === 'Technojet1') {
                    $password_correct = true;
                }
            }
        } elseif($user['user_type'] === 'customer' && $user['password'] === $current_password) {
            $password_correct = true;
        }

        if(!$password_correct) {
            header('Location: quan-ly-du-an?action=change_password&error=' . urlencode('Mật khẩu hiện tại không đúng'));
            exit;
        }
    }

    // Cập nhật mật khẩu mới (cho cả admin và customer)
    $update_success = updateUserPassword($user['id'], $new_password);

    if($update_success) {
        // Xóa session first time login
        unset($_SESSION['first_time_login']);
        unset($_SESSION['temp_company']);

        if($is_first_time) {
            header('Location: quan-ly-du-an?success=' . urlencode('Đặt mật khẩu thành công! Chào mừng bạn đến với hệ thống.'));
        } else {
            // Đổi mật khẩu thành công - Tự động đăng xuất và yêu cầu đăng nhập lại
            session_destroy();
            header('Location: quan-ly-du-an?success=' . urlencode('Đổi mật khẩu thành công! Vui lòng đăng nhập lại với mật khẩu mới.'));
        }
        exit;
    } else {
        $redirect_url = 'quan-ly-du-an?action=change_password&error=' . urlencode('Có lỗi xảy ra khi cập nhật mật khẩu');
        if($is_first_time) $redirect_url .= '&first_time=1';
        header('Location: ' . $redirect_url);
        exit;
    }
}

/* Xử lý upload tài liệu */
if(isset($_POST['upload_document'])) {
    $project_name = $_POST['project_name'];
    $company_name = $_POST['company_name'];
    $folder_type = $_POST['folder_type'];



    // Kiểm tra company_name không rỗng
    if(empty($company_name)) {
        if($_SESSION['user_type'] === 'admin') {
            $company_name = isset($_SESSION['selected_company']) ? $_SESSION['selected_company'] : '';
        } else {
            $company_name = $_SESSION['company_name'];
        }

    }

    if(!isset($_FILES['document_file']) || $_FILES['document_file']['error'] !== UPLOAD_ERR_OK) {
        header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&folder=' . urlencode($folder_type) . '&error=' . urlencode('Vui lòng chọn file để upload'));
        exit;
    }

    $file = $_FILES['document_file'];
    $file_name = $file['name'];
    $file_size = $file['size'];
    $file_tmp = $file['tmp_name'];
    $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

    // Kiểm tra loại file được phép
    $allowed_extensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar'];
    if(!in_array($file_ext, $allowed_extensions)) {
        header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&folder=' . urlencode($folder_type) . '&error=' . urlencode('Loại file không được phép. Chỉ chấp nhận: ' . implode(', ', $allowed_extensions)));
        exit;
    }

    // Kiểm tra kích thước file (50MB)
    if($file_size > 50 * 1024 * 1024) {
        header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&folder=' . urlencode($folder_type) . '&error=' . urlencode('File quá lớn. Kích thước tối đa 50MB'));
        exit;
    }

    // Tạo tên file unique
    $unique_name = time() . '_' . uniqid() . '.' . $file_ext;
    $upload_dir = 'uploads/' . $company_name . '/' . $project_name . '/' . $folder_type . '/';

    // Tạo thư mục nếu chưa có
    if(!file_exists($upload_dir)) {
        if(!mkdir($upload_dir, 0755, true)) {

            header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&folder=' . urlencode($folder_type) . '&error=' . urlencode('Không thể tạo thư mục upload'));
            exit;
        }
    }

    $file_path = $upload_dir . $unique_name;

    if(move_uploaded_file($file_tmp, $file_path)) {
        // Lưu thông tin file vào database
        global $pdo;

        try {
            // Tìm company ID
            $stmt = $pdo->prepare("SELECT id FROM project_companies WHERE name = ?");
            $stmt->execute([$company_name]);
            $company = $stmt->fetch();

            if(!$company) {

                header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&subfolder=' . urlencode($folder_type) . '&error=' . urlencode('Không tìm thấy công ty'));
                exit;
            }

            // Tìm project ID
            $stmt = $pdo->prepare("SELECT id FROM project_projects WHERE name = ? AND company_id = ?");
            $stmt->execute([$project_name, $company['id']]);
            $project = $stmt->fetch();

            if(!$project) {

                header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&subfolder=' . urlencode($folder_type) . '&error=' . urlencode('Không tìm thấy dự án'));
                exit;
            }

            $project_id = $project['id'];

            // Lấy user ID (admin hoặc customer)
            $uploaded_by = 1; // Default admin ID
            if($_SESSION['user_type'] === 'customer') {
                $stmt = $pdo->prepare("SELECT id FROM project_users WHERE username = ?");
                $stmt->execute([$_SESSION['company_name']]);
                $user = $stmt->fetch();
                if($user) $uploaded_by = $user['id'];
            } else {
                // Admin - lấy admin user ID
                $stmt = $pdo->prepare("SELECT id FROM project_users WHERE user_type = 'admin' LIMIT 1");
                $stmt->execute();
                $admin = $stmt->fetch();
                if($admin) $uploaded_by = $admin['id'];
            }



            // Thêm vào database
            $stmt = $pdo->prepare("INSERT INTO project_documents (project_id, folder_type, file_name, file_path, file_size, file_type, uploaded_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $result = $stmt->execute([$project_id, $folder_type, $file_name, $file_path, $file_size, $file_ext, $uploaded_by]);

            if(!$result) {

                header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&subfolder=' . urlencode($folder_type) . '&error=' . urlencode('Lỗi lưu thông tin file vào database'));
                exit;
            }

            header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&subfolder=' . urlencode($folder_type) . '&success=' . urlencode('Upload file thành công'));
            exit;

        } catch(Exception $e) {

            header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&subfolder=' . urlencode($folder_type) . '&error=' . urlencode('Lỗi database: ' . $e->getMessage()));
            exit;
        }
    } else {

        header('Location: quan-ly-du-an?action=documents&project=' . urlencode($project_name) . '&company=' . urlencode($company_name) . '&subfolder=' . urlencode($folder_type) . '&error=' . urlencode('Có lỗi xảy ra khi upload file'));
        exit;
    }
}

/* Xử lý download tài liệu */
if(isset($_GET['download']) && isset($_GET['file'])) {
    $file_path = $_GET['file'];

    // Kiểm tra file có tồn tại và thuộc về user hiện tại
    if(file_exists($file_path) && strpos($file_path, 'uploads/') === 0) {
        // Lấy thông tin file từ database
        global $pdo;
        $stmt = $pdo->prepare("SELECT * FROM project_documents WHERE file_path = ?");
        $stmt->execute([$file_path]);
        $document = $stmt->fetch();

        if($document) {
            // Set headers cho download
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $document['file_name'] . '"');
            header('Content-Length: ' . filesize($file_path));
            header('Cache-Control: must-revalidate');
            header('Pragma: public');

            // Output file
            readfile($file_path);
            exit;
        }
    }

    header('HTTP/1.0 404 Not Found');
    exit('File not found');
}

/* Xử lý xóa tài liệu */
if(isset($_GET['delete_document']) && isset($_GET['doc_id'])) {
    $doc_id = (int)$_GET['doc_id'];

    global $pdo;
    $stmt = $pdo->prepare("SELECT d.*, p.name as project_name, c.name as company_name
                          FROM project_documents d
                          JOIN project_projects p ON d.project_id = p.id
                          JOIN project_companies c ON p.company_id = c.id
                          WHERE d.id = ?");
    $stmt->execute([$doc_id]);
    $document = $stmt->fetch();

    if($document) {
        // Xóa file vật lý
        if(file_exists($document['file_path'])) {
            unlink($document['file_path']);
        }

        // Xóa record trong database
        $stmt = $pdo->prepare("DELETE FROM project_documents WHERE id = ?");
        $stmt->execute([$doc_id]);

        // Redirect về trang documents với đầy đủ parameters
        $redirect_params = [
            'action' => 'documents',
            'project' => $document['project_name'],
            'company' => $document['company_name'],
            'success' => 'Xóa tài liệu thành công'
        ];

        if(isset($_GET['subfolder'])) {
            $redirect_params['subfolder'] = $_GET['subfolder'];
        }

        $redirect_url = 'quan-ly-du-an?' . http_build_query($redirect_params);
        header('Location: ' . $redirect_url);
        exit;
    }

    // Nếu không tìm thấy document, redirect về dashboard
    header('Location: quan-ly-du-an?error=' . urlencode('Không tìm thấy tài liệu'));
    exit;
}

/* Xử lý quản lý projects */
if(isset($_POST['add_project'])) {
    $company_id = (int)$_POST['company_id'];
    $project_name = trim($_POST['project_name']);
    $project_description = trim($_POST['project_description']);

    if(empty($project_name)) {
        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&error=' . urlencode('Tên dự án không được để trống'));
        exit;
    }

    // Kiểm tra trùng tên project trong cùng company
    global $pdo;
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM project_projects WHERE company_id = ? AND name = ?");
    $stmt->execute([$company_id, $project_name]);
    if($stmt->fetchColumn() > 0) {
        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&error=' . urlencode('Tên dự án đã tồn tại'));
        exit;
    }

    // Thêm project mới
    $stmt = $pdo->prepare("INSERT INTO project_projects (name, description, company_id, progress, status) VALUES (?, ?, ?, ?, ?)");
    if($stmt->execute([$project_name, $project_description, $company_id, 0, 'planning'])) {
        // Tạo folder monitoring structure cho dự án mới
        createMonitorFolderStructure($project_name);

        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&success=' . urlencode('Thêm dự án thành công'));
    } else {
        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&error=' . urlencode('Có lỗi xảy ra khi thêm dự án'));
    }
    exit;
}

/* Xử lý gửi email support */
if(isset($_POST['recipient_email']) && isset($_POST['subject']) && isset($_POST['message'])) {
    $recipient_email = trim($_POST['recipient_email']);
    $subject = trim($_POST['subject']);
    $message = trim($_POST['message']);
    $company_name = $_SESSION['company_name'];

    // Validate email
    $allowed_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    if(!in_array($recipient_email, $allowed_emails)) {
        header('Location: quan-ly-du-an?error=' . urlencode('Email không hợp lệ'));
        exit;
    }

    // Tạo email content
    $email_subject = "[TechnoJet Support] " . $subject;
    $email_body = "Yêu cầu hỗ trợ từ: " . $company_name . "\n\n";
    $email_body .= "Nội dung:\n" . $message . "\n\n";
    $email_body .= "Thời gian: " . date('d/m/Y H:i:s') . "\n";
    $email_body .= "Hệ thống: TechnoJet Project Management";

    // Gửi email (sử dụng mail() function - có thể thay bằng PHPMailer)
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Reply-To: <EMAIL>\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

    if(mail($recipient_email, $email_subject, $email_body, $headers)) {
        header('Location: quan-ly-du-an?success=' . urlencode('Gửi email thành công! Chúng tôi sẽ phản hồi sớm nhất.'));
    } else {
        header('Location: quan-ly-du-an?error=' . urlencode('Có lỗi khi gửi email. Vui lòng thử lại.'));
    }
    exit;
}

if(isset($_POST['edit_project'])) {
    $project_id = (int)$_POST['project_id'];
    $company_id = (int)$_POST['company_id'];
    $project_name = trim($_POST['project_name']);
    $project_description = trim($_POST['project_description']);

    if(empty($project_name)) {
        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&error=' . urlencode('Tên dự án không được để trống'));
        exit;
    }

    // Kiểm tra trùng tên project (trừ project hiện tại)
    global $pdo;
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM project_projects WHERE company_id = ? AND name = ? AND id != ?");
    $stmt->execute([$company_id, $project_name, $project_id]);
    if($stmt->fetchColumn() > 0) {
        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&error=' . urlencode('Tên dự án đã tồn tại'));
        exit;
    }

    // Lấy tên dự án cũ trước khi update
    $stmt = $pdo->prepare("SELECT name FROM project_projects WHERE id = ? AND company_id = ?");
    $stmt->execute([$project_id, $company_id]);
    $old_project = $stmt->fetch();
    $old_project_name = $old_project ? $old_project['name'] : '';

    // Cập nhật project
    $stmt = $pdo->prepare("UPDATE project_projects SET name = ?, description = ? WHERE id = ? AND company_id = ?");
    if($stmt->execute([$project_name, $project_description, $project_id, $company_id])) {
        // Rename monitoring folder nếu tên thay đổi
        if($old_project_name && $old_project_name !== $project_name) {
            renameMonitoringFolder($old_project_name, $project_name);
        }

        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&success=' . urlencode('Cập nhật dự án thành công'));
    } else {
        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&error=' . urlencode('Có lỗi xảy ra khi cập nhật dự án'));
    }
    exit;
}

if(isset($_GET['delete_project'])) {
    $project_id = (int)$_GET['delete_project'];
    $company_id = (int)$_GET['company_id'];

    global $pdo;

    // Lấy tên dự án trước khi xóa để xóa monitoring folder
    $stmt = $pdo->prepare("SELECT name FROM project_projects WHERE id = ? AND company_id = ?");
    $stmt->execute([$project_id, $company_id]);
    $project = $stmt->fetch();
    $project_name = $project ? $project['name'] : '';

    // Xóa tất cả documents của project
    $stmt = $pdo->prepare("SELECT file_path FROM project_documents WHERE project_id = ?");
    $stmt->execute([$project_id]);
    $documents = $stmt->fetchAll();

    foreach($documents as $doc) {
        if(file_exists($doc['file_path'])) {
            unlink($doc['file_path']);
        }
    }

    // Xóa documents từ database
    $stmt = $pdo->prepare("DELETE FROM project_documents WHERE project_id = ?");
    $stmt->execute([$project_id]);

    // Xóa project
    $stmt = $pdo->prepare("DELETE FROM project_projects WHERE id = ? AND company_id = ?");
    if($stmt->execute([$project_id, $company_id])) {
        // Xóa monitoring folder
        if($project_name) {
            deleteMonitoringFolder($project_name);
        }

        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&success=' . urlencode('Xóa dự án thành công'));
    } else {
        header('Location: quan-ly-du-an?action=manage_projects&company=' . urlencode($_GET['company']) . '&company_id=' . $company_id . '&error=' . urlencode('Có lỗi xảy ra khi xóa dự án'));
    }
    exit;
}

/* ===== QUẢN LÝ KHÁCH HÀNG ===== */

/* Xử lý sửa thông tin khách hàng */
if(isset($_POST['edit_company'])) {
    requireAdmin();

    $company_id = (int)$_POST['company_id'];
    $company_name = trim($_POST['company_name']);
    $company_email = trim($_POST['company_email']);
    $company_address = trim($_POST['company_address']);
    $company_phone = trim($_POST['company_phone']);

    // Validation
    if(empty($company_name) || empty($company_email)) {
        redirectWithMessage('quan-ly-du-an', 'Tên công ty và email không được để trống', 'error');
    }

    if(!filter_var($company_email, FILTER_VALIDATE_EMAIL)) {
        redirectWithMessage('quan-ly-du-an', 'Email không hợp lệ', 'error');
    }

    // Kiểm tra trùng tên công ty
    global $pdo;
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM project_companies WHERE name = ? AND id != ?");
    $stmt->execute([$company_name, $company_id]);
    if($stmt->fetchColumn() > 0) {
        redirectWithMessage('quan-ly-du-an', 'Tên công ty đã tồn tại', 'error');
    }

    // Cập nhật thông tin công ty
    $stmt = $pdo->prepare("UPDATE project_companies SET name = ?, email = ?, address = ?, phone = ? WHERE id = ?");
    if($stmt->execute([$company_name, $company_email, $company_address, $company_phone, $company_id])) {
        redirectWithMessage('quan-ly-du-an', 'Cập nhật thông tin công ty thành công');
    } else {
        redirectWithMessage('quan-ly-du-an', 'Có lỗi xảy ra khi cập nhật', 'error');
    }
}

/* Xử lý vô hiệu hóa khách hàng */
if(isset($_GET['disable_company'])) {
    requireAdmin();

    $company_id = (int)$_GET['disable_company'];

    global $pdo;
    try {
        // Lấy thông tin công ty
        $stmt = $pdo->prepare("SELECT c.name, u.id as user_id FROM project_companies c JOIN project_users u ON c.user_id = u.id WHERE c.id = ?");
        $stmt->execute([$company_id]);
        $company = $stmt->fetch();

        if(!$company) {
            redirectWithMessage('quan-ly-du-an', 'Không tìm thấy công ty', 'error');
        }

        // Vô hiệu hóa user (set status = 'inactive')
        $stmt = $pdo->prepare("UPDATE project_users SET status = 'inactive', updated_at = NOW() WHERE id = ?");
        if($stmt->execute([$company['user_id']])) {


            redirectWithMessage('quan-ly-du-an', '✅ Đã vô hiệu hóa khách hàng "' . $company['name'] . '" thành công!');
        } else {
            redirectWithMessage('quan-ly-du-an', 'Có lỗi xảy ra khi vô hiệu hóa khách hàng', 'error');
        }

    } catch(Exception $e) {

        redirectWithMessage('quan-ly-du-an', 'Có lỗi xảy ra khi vô hiệu hóa khách hàng', 'error');
    }
}

/* Xử lý kích hoạt lại khách hàng */
if(isset($_GET['enable_company'])) {
    requireAdmin();

    $company_id = (int)$_GET['enable_company'];

    global $pdo;
    try {
        // Lấy thông tin công ty
        $stmt = $pdo->prepare("SELECT c.name, u.id as user_id FROM project_companies c JOIN project_users u ON c.user_id = u.id WHERE c.id = ?");
        $stmt->execute([$company_id]);
        $company = $stmt->fetch();

        if(!$company) {
            redirectWithMessage('quan-ly-du-an', 'Không tìm thấy công ty', 'error');
        }

        // Kích hoạt lại user (set status = 'active')
        $stmt = $pdo->prepare("UPDATE project_users SET status = 'active', updated_at = NOW() WHERE id = ?");
        if($stmt->execute([$company['user_id']])) {


            redirectWithMessage('quan-ly-du-an', '✅ Đã kích hoạt lại khách hàng "' . $company['name'] . '" thành công!');
        } else {
            redirectWithMessage('quan-ly-du-an', 'Có lỗi xảy ra khi kích hoạt khách hàng', 'error');
        }

    } catch(Exception $e) {

        redirectWithMessage('quan-ly-du-an', 'Có lỗi xảy ra khi kích hoạt khách hàng', 'error');
    }
}

/* Xử lý xóa khách hàng */
if(isset($_GET['delete_company'])) {
    requireAdmin();

    $company_id = (int)$_GET['delete_company'];

    global $pdo;
    try {
        $pdo->beginTransaction();

        // Lấy thông tin công ty trước khi xóa
        $stmt = $pdo->prepare("SELECT name FROM project_companies WHERE id = ?");
        $stmt->execute([$company_id]);
        $company = $stmt->fetch();

        if(!$company) {
            $pdo->rollBack();
            redirectWithMessage('quan-ly-du-an', 'Không tìm thấy công ty', 'error');
        }

        // Lấy danh sách tất cả dự án của công ty để xóa monitoring folders
        $stmt = $pdo->prepare("SELECT name FROM project_projects WHERE company_id = ?");
        $stmt->execute([$company_id]);
        $projects = $stmt->fetchAll();

        // Xóa tất cả monitoring folders của công ty
        foreach($projects as $project) {
            deleteMonitoringFolder($project['name']);
        }

        // Xóa cascade: documents -> projects -> users -> company
        $queries = [
            "DELETE d FROM project_documents d JOIN project_projects p ON d.project_id = p.id WHERE p.company_id = ?",
            "DELETE FROM project_projects WHERE company_id = ?",
            "DELETE u FROM project_users u JOIN project_companies c ON u.id = c.user_id WHERE c.id = ?",
            "DELETE FROM project_companies WHERE id = ?"
        ];

        foreach($queries as $query) {
            $stmt = $pdo->prepare($query);
            $stmt->execute([$company_id]);
        }

        // Xóa thư mục uploads
        $upload_dir = 'uploads/' . $company['name'];
        if(is_dir($upload_dir)) {
            deleteDirectory($upload_dir);
        }

        $pdo->commit();



        redirectWithMessage('quan-ly-du-an', '✅ Đã xóa khách hàng "' . $company['name'] . '" và tất cả dữ liệu liên quan thành công!');

    } catch(Exception $e) {
        $pdo->rollBack();

        redirectWithMessage('quan-ly-du-an', 'Có lỗi xảy ra khi xóa khách hàng', 'error');
    }
}

/* Xử lý đăng xuất */
if(isset($_GET['logout'])) {
    session_unset();
    header('Location: quan-ly-du-an');
    exit;
}

/* Nếu chưa đăng nhập, hiển thị form login */
if(!checkProjectAuth()) {
    $template = "project_management/login";
    return;
}

/* ===== SPECIAL ROUTES (không cần đăng nhập) ===== */

// Xử lý trang reset password (không cần đăng nhập)
if(isset($_GET['action']) && $_GET['action'] === 'reset_password') {
    $token = $_GET['token'] ?? '';
    $company_name = $_GET['company'] ?? '';

    // Verify token if provided
    if(!empty($token) && !empty($company_name)) {
        $user = verifyPasswordResetToken($token, $company_name);
        if(!$user) {
            $reset_error = "Link đặt lại mật khẩu không hợp lệ hoặc đã hết hạn. Vui lòng yêu cầu đặt lại mật khẩu mới.";
        }
    } else {
        $reset_error = "Thông tin không hợp lệ.";
    }

    include('templates/project_management/reset_password_tpl.php');
    exit;
}

/* ===== ROUTING LOGIC ===== */

// Lấy parameters từ URL
$action = $_GET['action'] ?? '';
$project = $_GET['project'] ?? '';
$folder = $_GET['folder'] ?? '';
$subfolder = $_GET['subfolder'] ?? '';

// Route mapping cho admin và customer
$admin_routes = [
    'company' => 'project_management/admin_company_dashboard',
    'view_as_customer' => 'project_management/customer_dashboard', // Admin xem như khách hàng
    'add_company' => 'project_management/add_company',
    'manage_projects' => 'project_management/manage_projects',
    'change_password' => 'project_management/change_password',
    'project' => 'project_management/project_folders',
    'documents' => 'project_management/documents',
    'monitoring' => 'project_management/monitoring',
    '' => 'project_management/admin_dashboard'
];

$customer_routes = [
    'change_password' => 'project_management/change_password',
    'project' => 'project_management/project_folders',
    'documents' => 'project_management/documents',
    'monitoring' => 'project_management/monitoring',
    '' => 'project_management/customer_dashboard'
];

// Xử lý logic đặc biệt cho admin
if($_SESSION['user_type'] === 'admin') {
    // Xử lý chọn company cho admin (cho cả company, view_as_customer và manage_projects action)
    if(in_array($action, ['company', 'view_as_customer', 'manage_projects']) && isset($_GET['company'])) {
        $selected_company = $_GET['company'];
        if(!empty($selected_company)) {
            $_SESSION['selected_company'] = $selected_company;
        }
    }

    $template = $admin_routes[$action] ?? $admin_routes[''];
} else {
    $template = $customer_routes[$action] ?? $customer_routes[''];
}

// Xử lý đặc biệt cho monitoring - check custom monitor trước
if($action === 'monitoring' && isset($_GET['project'])) {
    $project_name = $_GET['project'];
    $custom_monitor_path = getMonitoringFilePath($project_name);

    if($custom_monitor_path) {
        // Sử dụng custom monitor
        include($custom_monitor_path);
        exit; // Dừng xử lý, không load template mặc định
    }
    // Nếu không có custom monitor, tiếp tục dùng template mặc định
}

/* ===== ALARM MANAGEMENT FUNCTIONS ===== */

/**
 * Lưu alarm vào database
 * @param string $project_name - Tên dự án
 * @param string $alarm_name - Tên alarm
 * @param string $alarm_value - Giá trị alarm (ON/OFF)
 * @param string $description - Mô tả alarm
 * @return bool - True nếu thành công
 */
function saveAlarmToDatabase($project_name, $alarm_name, $alarm_value, $description = '') {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            INSERT INTO project_alarms (project_name, alarm_name, alarm_value, description, timestamp)
            VALUES (?, ?, ?, ?, NOW())
        ");

        return $stmt->execute([$project_name, $alarm_name, $alarm_value, $description]);
    } catch (Exception $e) {
        error_log("Error saving alarm: " . $e->getMessage());
        return false;
    }
}

/**
 * Lấy danh sách alarms theo dự án
 * @param string $project_name - Tên dự án
 * @param string $from_date - Ngày bắt đầu (optional)
 * @param string $to_date - Ngày kết thúc (optional)
 * @param string $alarm_name_filter - Lọc theo tên alarm (optional)
 * @param int $limit - Giới hạn số lượng (optional, 0 = không giới hạn)
 * @return array - Danh sách alarms
 */
function getProjectAlarms($project_name, $from_date = null, $to_date = null, $alarm_name_filter = null, $limit = 0) {
    global $pdo;

    try {
        $sql = "SELECT * FROM project_alarms WHERE project_name = ?";
        $params = [$project_name];

        // Thêm filter theo ngày
        if ($from_date) {
            $sql .= " AND timestamp >= ?";
            $params[] = $from_date;
        }

        if ($to_date) {
            $sql .= " AND timestamp <= ?";
            $params[] = $to_date;
        }

        // Thêm filter theo tên alarm
        if ($alarm_name_filter) {
            $sql .= " AND alarm_name LIKE ?";
            $params[] = "%{$alarm_name_filter}%";
        }

        // Sắp xếp theo thời gian mới nhất
        $sql .= " ORDER BY timestamp DESC";

        // Thêm limit nếu có
        if ($limit > 0) {
            $sql .= " LIMIT ?";
            $params[] = $limit;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting alarms: " . $e->getMessage());
        return [];
    }
}

/**
 * Lấy thống kê alarms
 * @param string $project_name - Tên dự án
 * @return array - Thống kê alarms
 */
function getAlarmStats($project_name) {
    global $pdo;

    try {
        // Tổng số alarms
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM project_alarms WHERE project_name = ?");
        $stmt->execute([$project_name]);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Alarms hôm nay
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as today
            FROM project_alarms
            WHERE project_name = ? AND DATE(timestamp) = CURDATE()
        ");
        $stmt->execute([$project_name]);
        $today = $stmt->fetch(PDO::FETCH_ASSOC)['today'];

        // Alarms đang active (ON)
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as active
            FROM project_alarms
            WHERE project_name = ? AND alarm_value = 'ON' AND DATE(timestamp) = CURDATE()
        ");
        $stmt->execute([$project_name]);
        $active = $stmt->fetch(PDO::FETCH_ASSOC)['active'];

        return [
            'total' => (int)$total,
            'today' => (int)$today,
            'active' => (int)$active
        ];
    } catch (Exception $e) {
        error_log("Error getting alarm stats: " . $e->getMessage());
        return ['total' => 0, 'today' => 0, 'active' => 0];
    }
}

/**
 * Xóa alarms cũ (giữ lại N records gần nhất)
 * @param string $project_name - Tên dự án
 * @param int $keep_records - Số records giữ lại (default: 1000)
 * @return bool - True nếu thành công
 */
function cleanupOldAlarms($project_name, $keep_records = 1000) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            DELETE FROM project_alarms
            WHERE project_name = ?
            AND id NOT IN (
                SELECT id FROM (
                    SELECT id FROM project_alarms
                    WHERE project_name = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                ) as keep_table
            )
        ");

        return $stmt->execute([$project_name, $project_name, $keep_records]);
    } catch (Exception $e) {
        error_log("Error cleaning up alarms: " . $e->getMessage());
        return false;
    }
}

/* ===== AJAX HANDLERS FOR ALARMS ===== */

// Xử lý AJAX requests cho alarms
if (isset($_POST['action']) && $_POST['action'] === 'alarm_action') {
    header('Content-Type: application/json');

    $response = ['success' => false, 'message' => 'Invalid request'];

    if (!checkProjectAuth()) {
        $response['message'] = 'Unauthorized';
        echo json_encode($response);
        exit;
    }

    $alarm_action = $_POST['alarm_action'] ?? '';
    $project_name = $_POST['project_name'] ?? '';

    switch ($alarm_action) {
        case 'save':
            $alarm_name = $_POST['alarm_name'] ?? '';
            $alarm_value = $_POST['alarm_value'] ?? '';
            $description = $_POST['description'] ?? '';

            if ($project_name && $alarm_name && $alarm_value) {
                $success = saveAlarmToDatabase($project_name, $alarm_name, $alarm_value, $description);
                $response = [
                    'success' => $success,
                    'message' => $success ? 'Alarm saved successfully' : 'Failed to save alarm'
                ];
            } else {
                $response['message'] = 'Missing required parameters';
            }
            break;

        case 'load':
            $from_date = $_POST['from_date'] ?? null;
            $to_date = $_POST['to_date'] ?? null;
            $name_filter = $_POST['name_filter'] ?? null;

            if ($project_name) {
                $alarms = getProjectAlarms($project_name, $from_date, $to_date, $name_filter);
                $response = [
                    'success' => true,
                    'data' => $alarms,
                    'total' => count($alarms)
                ];
            } else {
                $response['message'] = 'Missing project name';
            }
            break;

        case 'stats':
            if ($project_name) {
                $stats = getAlarmStats($project_name);
                $response = [
                    'success' => true,
                    'data' => $stats
                ];
            } else {
                $response['message'] = 'Missing project name';
            }
            break;

        case 'cleanup':
            if ($project_name) {
                $success = cleanupOldAlarms($project_name);
                $response = [
                    'success' => $success,
                    'message' => $success ? 'Old alarms cleaned up' : 'Failed to cleanup alarms'
                ];
            } else {
                $response['message'] = 'Missing project name';
            }
            break;
    }

    echo json_encode($response);
    exit;
}

?>
