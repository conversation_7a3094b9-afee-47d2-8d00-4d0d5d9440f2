/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
import { UAEventType } from "node-opcua-address-space-base";
import { DataValue } from "node-opcua-data-value";
import { NodeId } from "node-opcua-nodeid";
import { VariantOptions } from "node-opcua-variant";
import { UAShelvedStateMachineEx } from "../../source/interfaces/state_machine/ua_shelved_state_machine_ex";
import { InstantiateLimitAlarmOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_limit_alarm_options";
import { UALimitAlarmEx } from "../../source/interfaces/alarms_and_conditions/ua_limit_alarm_ex";
import { NamespacePrivate } from "../namespace_private";
import { UAAlarmConditionImpl } from "./ua_alarm_condition_impl";
export declare interface UALimitAlarmImpl extends UALimitAlarmEx, UAAlarmConditionImpl {
    on(eventName: string, eventHandler: any): this;
    once(eventName: string, eventHandler: any): this;
}
export interface UALimitAlarmImpl extends UALimitAlarmEx {
    shelvingState?: UAShelvedStateMachineEx;
}
export declare class UALimitAlarmImpl extends UAAlarmConditionImpl implements UALimitAlarmEx {
    /**
     */
    static instantiate(namespace: NamespacePrivate, limitAlarmTypeId: UAEventType | NodeId | string, options: InstantiateLimitAlarmOptions, data?: Record<string, VariantOptions>): UALimitAlarmImpl;
    /**
     */
    getHighHighLimit(): number;
    /**
     */
    getHighLimit(): number;
    /**
     */
    getLowLimit(): number;
    /**
     */
    getLowLowLimit(): number;
    /**
     */
    setHighHighLimit(value: number): void;
    /**
     */
    setHighLimit(value: number): void;
    /**
     */
    setLowLimit(value: number): void;
    /**
     */
    setLowLowLimit(value: number): void;
    protected _onInputDataValueChange(dataValue: DataValue): void;
    protected _setStateBasedOnInputValue(value: number): void;
    protected _watchLimits(): void;
    protected evaluateConditionsAfterEnabled(): void;
}
