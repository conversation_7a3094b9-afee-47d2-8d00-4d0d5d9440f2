{"version": 3, "file": "ua_non_exclusive_deviation_alarm_impl.js", "sourceRoot": "", "sources": ["../../../src/alarms_and_conditions/ua_non_exclusive_deviation_alarm_impl.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,yDAA2C;AAU3C,qEAKkC;AAClC,+DAAyD;AACzD,2FAAmF;AASnF,MAAa,gCAAiC,SAAQ,gEAA4B;IACvE,MAAM,CAAC,WAAW,CACrB,SAA2B,EAC3B,IAAqB,EACrB,OAAqC,EACrC,IAAqC;QAErC,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;QAE5C,MAAM,8BAA8B,GAAG,YAAY,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;QACpG,0BAA0B;QAC1B,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC/D,CAAC;QACD,IAAA,0BAAM,EAAC,IAAI,KAAK,8BAA8B,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEtE,MAAM,KAAK,GAAG,gEAA4B,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAqC,CAAC;QAC3H,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,gCAAgC,CAAC,SAAS,CAAC,CAAC;QAEzE,IAAA,0BAAM,EAAC,KAAK,YAAY,gCAAgC,CAAC,CAAC;QAC1D,IAAA,0BAAM,EAAC,KAAK,YAAY,gEAA4B,CAAC,CAAC;QACtD,IAAA,0BAAM,EAAC,KAAK,YAAY,sCAAgB,CAAC,CAAC;QAE1C,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAEjC,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,0BAA0B,CAAC,KAAa;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACpD,CAAC;QACD,IAAA,0BAAM,EAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,kCAAkC,CAAC,CAAC;QACpE,iCAAiC;QACjC,KAAK,CAAC,0BAA0B,CAAC,KAAK,GAAG,aAAa,CAAC,CAAC;IAC5D,CAAC;IAEM,mBAAmB;QACtB,OAAO,iEAAwC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;IAEM,gBAAgB;QACnB,OAAO,8DAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEM,0BAA0B,CAAC,SAAoB;QAClD,uEAA8C,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACzE,CAAC;IAEM,iBAAiB,CAAC,OAAY;QACjC,OAAO,8DAAqC,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrE,CAAC;CACJ;AArDD,4EAqDC"}