"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConditionInfoImpl = void 0;
/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_data_model_1 = require("node-opcua-data-model");
/**
 * @private
 */
class ConditionInfoImpl {
    constructor(options) {
        this.message = null;
        this.quality = null;
        this.severity = 0;
        this.retain = false;
        this.severity = null;
        this.quality = null;
        this.message = null;
        this.retain = null;
        if (Object.prototype.hasOwnProperty.call(options, "message") && options.message) {
            this.message = node_opcua_data_model_1.LocalizedText.coerce(options.message);
        }
        if (Object.prototype.hasOwnProperty.call(options, "quality") && options.quality !== null) {
            this.quality = options.quality;
        }
        if (Object.prototype.hasOwnProperty.call(options, "severity") && options.severity !== null) {
            (0, node_opcua_assert_1.assert)(typeof options.severity === "number");
            this.severity = options.severity;
        }
        if (Object.prototype.hasOwnProperty.call(options, "retain") && options.retain !== null) {
            (0, node_opcua_assert_1.assert)(typeof options.retain === "boolean");
            this.retain = options.retain;
        }
    }
    /**
     *
     */
    isDifferentFrom(otherConditionInfo) {
        return (this.severity !== otherConditionInfo.severity ||
            this.quality !== otherConditionInfo.quality ||
            this.message !== otherConditionInfo.message);
    }
}
exports.ConditionInfoImpl = ConditionInfoImpl;
//# sourceMappingURL=condition_info_impl.js.map