import { IAddressSpace, INamespace } from "node-opcua-address-space-base";
import { TranslationTable } from "../../source/xml_writer";
export declare function _recomputeRequiredModelsFromTypes(namespace: INamespace, cache?: Map<number, {
    requiredNamespaceIndexes: number[];
    nbTypes: number;
}>): {
    requiredNamespaceIndexes: number[];
    nbTypes: number;
};
export declare function _recomputeRequiredModelsFromTypes2(namespace: INamespace, cache?: Map<number, {
    requiredNamespaceIndexes: number[];
    nbTypes: number;
}>): {
    requiredNamespaceIndexes: number[];
};
export declare function _getCompleteRequiredModelsFromValuesAndReferences(namespace: INamespace, priorityList: number[], cache?: Map<number, {
    requiredNamespaceIndexes: number[];
    nbTypes: number;
}>): number[];
/**
 *
 * @param namespace
 * @returns the order
 *
 *      ---
 *  ua, own , di  => 0 , 2,  1
 *
 *      ---
 *  ua, own , di , kitchen , own2,  adi  => 0 , 2,  3, 1
 *
 *                           ---
 *  ua, own , di , kitchen , own2,  adi  => 0 , 2,  3,  5, 1
 */
export declare function constructNamespacePriorityTable(addressSpace: IAddressSpace): {
    loadingOrder: number[];
    priorityTable: number[];
};
export declare function constructNamespaceDependency(namespace: INamespace, priorityTable?: number[]): INamespace[];
/**
 * @private
 */
export declare function _constructNamespaceTranslationTable(dependency: INamespace[], exportedNamespace: INamespace): TranslationTable;
