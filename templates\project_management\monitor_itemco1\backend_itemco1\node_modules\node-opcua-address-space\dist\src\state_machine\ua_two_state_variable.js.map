{"version": 3, "file": "ua_two_state_variable.js", "sourceRoot": "", "sources": ["../../../src/state_machine/ua_two_state_variable.ts"], "names": [], "mappings": ";;;AA+HA,kFAcC;AAED,8DAUC;AA6ND,oDAwCC;AA9ZG;;EAED;AACH,yDAA2C;AAE3C,+DAAuD;AACvD,iEAA+G;AAE/G,yDAA0D;AAC1D,yDAA+C;AAC/C,mEAAqF;AACrF,2DAAoE;AACpE,2DAA8C;AAK9C,oBAAoB;AACpB,uFAAkF;AAGlF,gBAAgB;AAChB,0DAAsE;AAItE,MAAM,mCAAmC,GAAG,IAAA,iCAAa,EAAC,iBAAiB,CAAC,CAAC;AAC7E,MAAM,oCAAoC,GAAG,IAAA,iCAAa,EAAC,kBAAkB,CAAC,CAAC;AAE/E,mDAAmD;AACnD,2BAA2B;AAC3B,4FAA4F;AAC5F,6FAA6F;AAC7F,0DAA0D;AAC1D,kEAAkE;AAClE,EAAE;AACF,yBAAyB;AACzB,wCAAwC;AACxC,iCAAiC;AACjC,oCAAoC;AACpC,yBAAyB;AACzB,EAAE;AACF,sDAAsD;AACtD,gGAAgG;AAChG,EAAE;AACF,gGAAgG;AAChG,2FAA2F;AAC3F,0FAA0F;AAC1F,0FAA0F;AAC1F,0FAA0F;AAC1F,0FAA0F;AAC1F,kCAAkC;AAClC,uCAAuC;AACvC,sGAAsG;AACtG,mCAAmC;AACnC,wCAAwC;AACxC,sGAAsG;AAEtG,SAAS,qBAAqB,CAAC,IAA0B,EAAE,SAAsB,EAAE,OAA0B;IACzG,wEAAwE;IACxE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,MAAM,cAAc,GAAG,OAAO,EAAE,cAAc,IAAI,IAAI,IAAI,EAAE,CAAC;QAC7D,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,6BAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;IACnG,CAAC;AACL,CAAC;AAED,SAAS,8BAA8B,CAAC,IAA4B,EAAC,OAA0B;IAC3F,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,MAAM,uBAAuB,GAAG,OAAO,EAAE,uBAAuB,IAAI,IAAI,IAAI,EAAE,CAAC;QAE/E,8DAA8D;QAC9D,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC;YAC5C,QAAQ,EAAE,6BAAQ,CAAC,QAAQ;YAC3B,KAAK,EAAE,uBAAuB;SACjC,CAAC,CAAC;IACP,CAAC;AACL,CAAC;AAED,SAAS,wBAAwB,CAC7B,IAA4B;IAE5B,MAAM,mBAAmB,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAC1D,IAAI,mBAAmB,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;QAC7C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IACD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAElC,IAAI,aAAa,CAAC;IAClB,IAAI,SAAS,EAAE,CAAC;QACZ,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,uCAAe,CAAC,OAAO,CAAC,CAAC;IAC9F,CAAC;SAAM,CAAC;QACJ,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,uCAAe,CAAC,OAAO,CAAC,CAAC;IAC/F,CAAC;IACD,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAM,EAAE,EAAE;QAC5C,cAAc;IAClB,CAAC,CAAC,CAAC;IAEH,OAAO,mBAAmB,CAAC;AAC/B,CAAC;AAED,SAAS,uBAAuB,CAAC,IAA4B;IACzD,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC;IAEtC,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;QACnC,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,EAAuD,CAAC;QAClF,EAAE,CAAC,KAAK,GAAG,IAAI,4BAAO,CAAC;YACnB,QAAQ,EAAE,6BAAQ,CAAC,aAAa;YAChC,KAAK,EAAE,IAAA,2CAAmB,EAAC,EAAE,CAAE;SAClC,CAAoD,CAAC;QAEtD,OAAO,EAAE,CAAC;IACd,CAAC;IACD,IAAA,0BAAM,EAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,KAAK,6BAAQ,CAAC,OAAO,CAAC,CAAC;IACtD,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;IAExC,iGAAiG;IACjG,sGAAsG;IAEtG,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;IACrC,UAAU,CAAC,KAAK,GAAG,IAAI,4BAAO,CAAC;QAC3B,QAAQ,EAAE,6BAAQ,CAAC,aAAa;QAChC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;KAChE,CAAC,CAAC;IACH,OAAO,UAA+D,CAAC;AAC3E,CAAC;AAED,SAAgB,mCAAmC,CAC/C,IAAgB,EAChB,OAA0C;IAE1C,IAAA,0BAAM,EAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,eAAe,CAAC,CAAC;IACnE,IAAA,0BAAM,EAAC,IAAI,CAAC,uBAAuB,KAAK,CAAC,CAAC,CAAC;IAC3C,IAAA,0BAAM,EAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,sBAAsB,CAAC,CAAC;IAChF,IAAA,0BAAM,EAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,eAAe,CAAC,CAAC;IACnE,IAAA,0BAAM,EAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC;IAEnH,yCAAyC;IACzC,MAAM,KAAK,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;IAC7C,IAA+B,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACrD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,SAAgB,yBAAyB,CAAC,IAAgB;IACtD,IAAI,IAAI,YAAY,sBAAsB,EAAE,CAAC;QACzC,OAAO,IAAuC,CAAC;IACnD,CAAC;IACD,uBAAuB;IACvB,IAAI,CAAC,CAAC,IAAI,YAAY,iCAAc,CAAC,EAAE,CAAC;QACpC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAC1D,CAAC;IACD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAC9D,OAAO,IAAuC,CAAC;AACnD,CAAC;AACD,IAAA,6CAAoB,EAAC,sCAAe,CAAC,oBAAoB,EAAE,yBAAyB,CAAC,CAAC;AAqCtF;;GAEG;AACH,MAAa,sBAAuB,SAAQ,kCAAsD;IAI9F,YAAmB,IAAS;QACxB,KAAK,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,IAAI,iBAAiB;QACjB,OAAO,KAAK,CAAC,iBAAyC,CAAC;IAC3D,CAAC;IACD,IAAI,gBAAgB;QAChB,OAAO,KAAK,CAAC,gBAAwC,CAAC;IAC1D,CAAC;IAEM,UAAU,CAAC,OAA0C;QACxD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,IAAA,0BAAM,EAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC7B,IAAA,0BAAM,EAAC,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC;YAC9C,IAAA,0BAAM,EAAC,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC;YAE/C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;oBAC/B,QAAQ,EAAE,6BAAQ,CAAC,aAAa;oBAChC,KAAK,EAAE,IAAA,2CAAmB,EAAC,OAAO,CAAC,UAAU,CAAC;iBACjD,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,GAAG,IAAA,2CAAmB,EAAC,OAAO,CAAC,SAAS,CAAE,CAAC,IAAK,CAAC;YACpE,CAAC;YACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;oBAC9B,QAAQ,EAAE,6BAAQ,CAAC,aAAa;oBAChC,KAAK,EAAE,IAAA,2CAAmB,EAAC,OAAO,CAAC,SAAS,CAAC;iBAChD,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,WAAW,GAAG,IAAA,2CAAmB,EAAC,OAAO,CAAC,UAAU,CAAE,CAAC,IAAK,CAAC;YACtE,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,YAAY,CAAC;gBACd,SAAS,EAAE,KAAK;gBAChB,MAAM,EAAE,OAAO,CAAC,gBAAgB;gBAChC,aAAa,EAAE,iBAAiB;aACnC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,YAAY,CAAC;gBACd,SAAS,EAAE,KAAK;gBAChB,MAAM,EAAE,OAAO,CAAC,iBAAiB;gBACjC,aAAa,EAAE,kBAAkB;aACpC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,EAAE,CAAC,kBAAkB,CACtB;gBACI,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,KAAK;aACf,EACD,oCAAW,CAAC,qBAAqB,CACpC,CAAC;QACN,CAAC;aAAM,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,EAAE,CAAC,kBAAkB,CACtB;gBACI,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,OAAO,CAAC,KAAK;aACvB,EACD,oCAAW,CAAC,IAAI,CACnB,CAAC;QACN,CAAC;aAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;YACzE,IAAA,0BAAM,EAAE,OAAO,CAAC,KAAqB,CAAC,QAAQ,KAAK,6BAAQ,CAAC,OAAO,CAAC,CAAC;YACrE,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,OAAO,CAAC,KAAoB,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;QAC/E,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAEM,eAAe;QAClB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,6EAA6E;YAC7E,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;YAC3E,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBAC/B,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,GAAG,EAAE,CAAC,8BAA8B,CAAC,IAAI,CAAC,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC;QAED,mFAAmF;QACnF,0FAA0F;QAC1F,4BAA4B;QAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,EAAE,GAAG,EAAE;YAC7B,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5D,sDAAsD;QAEtD,mBAAmB;QACnB,gDAAgD;QAChD,yFAAyF;QACzF,gGAAgG;QAChG,6FAA6F;QAC7F,oCAAoC;QACpC,EAAE;QACF,sEAAsE;QACtE,qCAAqC;QACrC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,eAAe,EAAE,GAAG,EAAE;gBAC5B,IAAI,CAAC,oBAAwC,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;YAC3G,CAAC,CAAC,CAAC;YACF,IAAI,CAAC,oBAAwC,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3G,CAAC;IACL,CAAC;IACD;OACG;IACI,QAAQ,CAAC,SAAkB,EAAE,OAA0B;QAC1D,IAAA,0BAAM,EAAC,OAAO,SAAS,KAAK,SAAS,CAAC,CAAC;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,EAAG,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;QACvC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC1D,OAAO,CAAC,gBAAgB;QAC5B,CAAC;QACD,EAAE;QACF,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,IAAI,4BAAO,CAAC,EAAE,QAAQ,EAAE,6BAAQ,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAC1F,qBAAqB,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAChD,8BAA8B,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;OACG;IACI,QAAQ;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,EAAG,CAAC,SAAS,EAAE,CAAC;QACvC,IAAA,0BAAM,EAAC,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;QACtC,IAAA,0BAAM,EAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,KAAK,6BAAQ,CAAC,OAAO,CAAC,CAAC;QACtD,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;IACjC,CAAC;IAED;OACG;IACI,gBAAgB;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACnC,IAAA,0BAAM,EAAC,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;QACtC,IAAA,0BAAM,EAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,KAAK,6BAAQ,CAAC,aAAa,CAAC,CAAC;QAC5D,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACxD,CAAC;IACM,YAAY;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAA,2CAAmB,EAAC,IAAI,CAAC,UAAU,IAAI,MAAM,CAAE,CAAC;IACrH,CAAC;IACM,aAAa;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAA,2CAAmB,EAAC,IAAI,CAAC,WAAW,IAAI,OAAO,CAAE,CAAC;IACzH,CAAC;IACD,gFAAgF;IAChF,sEAAsE;IACtE,2FAA2F;IAC3F,yBAAyB;IACzB,wCAAwC;IACxC,wDAAwD;IACxD,0GAA0G;IAC1G,uGAAuG;IACvG,yGAAyG;IACzG,kEAAkE;IAC3D,uBAAuB,CAAC,SAAsB;QACjD,KAAK,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;QAEzC,IACI,SAAS,CAAC,SAAS;YACnB,CAAC,IAAA,8BAAU,EAAC,SAAS,CAAC,aAAa,EAAE,mCAAmC,CAAC;gBACrE,IAAA,8BAAU,EAAC,SAAS,CAAC,aAAa,EAAE,oCAAoC,CAAC,CAAC,EAChF,CAAC;YACC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;YACvC,mBAAmB;YACnB,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAe,CAAC;YACvE,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;QAC7F,CAAC;IACL,CAAC;CACJ;AAlLD,wDAkLC;AAED,SAAgB,oBAAoB,CAAC,SAAqB,EAAE,OAAmC;IAC3F,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;IAE5C,MAAM,oBAAoB,GAAG,YAAY,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;IACnF,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACxD,CAAC;IAED,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;IAC5C,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;QACpB,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC;IACD,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;QACrB,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACzC,CAAC;IAED,iCAAiC;IACjC,OAAO,CAAC,uBAAuB,GAAG,CAAC,CAAC;IAEpC,MAAM,IAAI,GAAG,oBAAoB,CAAC,WAAW,CAAC;QAC1C,UAAU,EAAE,OAAO,CAAC,UAAU;QAE9B,MAAM,EAAE,OAAO,CAAC,MAAM;QAEtB,WAAW,EAAE,OAAO,CAAC,WAAW;QAEhC,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,WAAW,EAAE,OAAO,CAAC,WAAW;QAEhC,aAAa,EAAE,OAAO,CAAC,aAAa;QAEpC,QAAQ,EAAE,IAAA,iCAAa,EAAC,6BAAQ,CAAC,aAAa,CAAC;QAE/C,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;QAExD,SAAS,EAAE,OAAO,CAAC,SAAS;KAC/B,CAAC,CAAC;IAEH,MAAM,KAAK,GAAG,mCAAmC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACjE,OAAO,KAAK,CAAC;AACjB,CAAC"}