import { ByteString } from "node-opcua-basic-types";
import { LocalizedText, LocalizedTextLike } from "node-opcua-data-model";
import { NodeId } from "node-opcua-nodeid";
import { StatusCode } from "node-opcua-status-code";
import { TimeZoneDataType } from "node-opcua-types";
import { DataType, VariantLike, VariantOptions } from "node-opcua-variant";
import { UAVariable, INamespace, ISessionContext, UAEventType, UAObject, UAProperty } from "node-opcua-address-space-base";
import { ConditionInfoOptions } from "../../source/interfaces/alarms_and_conditions/condition_info_i";
import { UAConditionEx } from "../../source/interfaces/alarms_and_conditions/ua_condition_ex";
import { ConditionSnapshot } from "../../source/interfaces/alarms_and_conditions/condition_snapshot";
import { ISetStateOptions } from "../../source/interfaces/i_set_state_options";
import { AddressSpacePrivate } from "../address_space_private";
import { UABaseEventImpl } from "./ua_base_event_impl";
export declare interface UAConditionImpl extends UAConditionEx, UABaseEventImpl {
    on(eventName: string, eventHandler: any): this;
    once(eventName: string, eventHandler: any): this;
    conditionClassId: UAProperty<NodeId, /*c*/ DataType.NodeId>;
    conditionClassName: UAProperty<LocalizedText, /*c*/ DataType.LocalizedText>;
    conditionSubClassId?: UAProperty<NodeId[], DataType.NodeId>;
    conditionSubClassName?: UAProperty<LocalizedText[], DataType.LocalizedText>;
}
/**
 *
 *   └─ ConditionType
 *    ├─ DialogConditionType
 *    └─ AcknowledgeableConditionType
 *       └─ AlarmConditionType
 *          ├─ LimitAlarmType
 *          │  ├─ ExclusiveLimitAlarmType
 *          │  │  ├─ ExclusiveLevelAlarmType
 *          │  │  ├─ ExclusiveDeviationAlarmType
 *          │  │  └─ ExclusiveRateOfChangeAlarmType
 *          │  └─ NonExclusiveLimitAlarmType
 *          │     ├─ NonExclusiveLevelAlarmType
 *          │     ├─ NonExclusiveDeviationAlarmType
 *          │     └─ NonExclusiveRateOfChangeAlarmType
 *          └─ DiscreteAlarmType
 *             ├─ OffNormalAlarmType
 *             │  ├─ SystemOffNormalAlarmType
 *             │  │  └─ CertificateExpirationAlarmType
 *             │  └─ TripAlarmType
 *
 */
export declare class UAConditionImpl extends UABaseEventImpl implements UAConditionEx {
    static defaultSeverity: number;
    static typeDefinition: NodeId;
    static instantiate(namespace: INamespace, conditionTypeId: NodeId | string | UAEventType, options: any, data?: Record<string, VariantOptions>): UAConditionImpl;
    static install_condition_refresh_handle(addressSpace: AddressSpacePrivate): void;
    /**
     *
     * Helper method to handle condition methods that takes a branchId and a comment
     *
     */
    static with_condition_method(inputArguments: VariantLike[], context: ISessionContext, callback: (err: Error | null, result?: {
        statusCode: StatusCode;
    }) => void, inner_func: (eventId: ByteString, comment: LocalizedText, branch: ConditionSnapshot, conditionNode: UAConditionImpl) => StatusCode): void;
    private _branch0;
    private _previousRetainFlag;
    private _branches;
    /**
     * @private
     */
    initialize(): void;
    /**
     * @private
     */
    post_initialize(): void;
    getBranchCount(): number;
    getBranches(): ConditionSnapshot[];
    getBranchIds(): NodeId[];
    /**
     */
    createBranch(): ConditionSnapshot;
    /**
     */
    deleteBranch(branch: ConditionSnapshot): void;
    /**
     */
    getEnabledState(): boolean;
    /**
     */
    getEnabledStateAsString(): string;
    /**
     * returns {StatusCode} StatusCodes.Good if successful or BadConditionAlreadyEnabled/BadConditionAlreadyDisabled
     * @private
     */
    _setEnabledState(requestedEnabledState: boolean, options?: ISetStateOptions): StatusCode;
    /**
     *
     * @private
     */
    setEnabledState(requestedEnabledState: boolean, options?: ISetStateOptions): StatusCode;
    /**
     */
    setReceiveTime(time: Date): void;
    /**
     */
    setLocalTime(time: TimeZoneDataType): void;
    /**
     */
    setTime(time: Date): void;
    _assert_valid(): void;
    /**
     */
    conditionOfNode(): UAObject | UAVariable | null;
    /**
     * Raise a Instance Event
     * (see also UAObject#raiseEvent to raise a transient event)
     * @param branch the condition branch to raise
     * @param renewEventId true if event Id of the condition branch should be renewed
     */
    raiseConditionEvent(branch: ConditionSnapshot, renewEventId: boolean): void;
    /**
     *
     * @param conditionInfo {ConditionInfo}
     *
     */
    raiseNewCondition(conditionInfo: ConditionInfoOptions): void;
    raiseNewBranchState(branch: ConditionSnapshot): void;
    /**
     */
    currentBranch(): ConditionSnapshot;
    _resend_conditionEvents(): 0 | 1;
    /**
     * @private
     */
    _raiseAuditConditionCommentEvent(sourceName: string, conditionEventId: Buffer, comment: LocalizedTextLike): void;
    protected _findBranchForEventId(eventId: Buffer): ConditionSnapshot | null;
    protected evaluateConditionsAfterEnabled(): void;
}
