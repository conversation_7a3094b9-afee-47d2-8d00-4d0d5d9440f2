{"version": 3, "file": "construct_namespace_dependency.js", "sourceRoot": "", "sources": ["../../../src/nodeset_tools/construct_namespace_dependency.ts"], "names": [], "mappings": ";;;;;AAiBA,8EAiJC;AAED,gFA0BC;AAED,8GA4CC;AAgBD,0EA4DC;AAGD,oEAKC;AAKD,kFAuBC;AA3VD,iEAAkD;AAElD,mEAAkD;AAElD,2DAA6C;AAC7C,0EAAuC;AACvC,uDAAkE;AAElE,sDAAmE;AAInE,MAAM,UAAU,GAAG,IAAA,kCAAe,EAAC,UAAU,CAAC,CAAC;AAC/C,MAAM,QAAQ,GAAG,IAAA,gCAAa,EAAC,UAAU,CAAC,CAAC;AAE3C,sDAAsD;AACtD,SAAgB,iCAAiC,CAC7C,SAAqB,EACrB,KAA4E;IAE5E,IAAI,SAAS,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,EAAE,wBAAwB,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACxD,CAAC;IACD,IAAI,KAAK,EAAE,CAAC;QACR,IAAI,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAE,CAAC;QACvC,CAAC;IACL,CAAC;IACD,MAAM,wBAAwB,GAAG,CAAC,CAAC,CAAC,CAAC;IACrC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAC;IAC5C,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzB,MAAM,UAAU,GAAG,SAA6B,CAAC;IACjD,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;IAC5C,MAAM,KAAK,GAAG,CAAC,iCAAS,CAAC,YAAY,EAAE,iCAAS,CAAC,UAAU,EAAE,iCAAS,CAAC,aAAa,EAAE,iCAAS,CAAC,QAAQ,CAAC,CAAC;IAC1G,MAAM,SAAS,GAAG,CAAC,iCAAS,CAAC,QAAQ,EAAE,iCAAS,CAAC,MAAM,EAAE,iCAAS,CAAC,MAAM,CAAC,CAAC;IAE3E,MAAM,QAAQ,GAAG,CAAC,aAAqB,EAAE,EAAE;QACvC,IAAI,aAAa,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7E,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACrC,wBAAwB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAgB,IAAI,GAAG,EAAU,CAAC;IAExD,SAAS,oBAAoB,CAAC,KAAqB;QAC/C,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;QAChC,MAAM,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC;QAC1C,QAAQ,CAAC,cAAc,CAAC,CAAC;QACzB,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,YAAY,EAAE,CAAC;YACf,gBAAgB,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;IACD,SAAS,gBAAgB,CAAC,YAAwB;QAC9C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QACD,MAAM,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC;QACrC,IAAI,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;YAC5C,OAAO;QACX,CAAC;QACD,uBAAuB;QACvB,IAAI,YAAY,CAAC,SAAS,KAAK,iCAAS,CAAC,QAAQ,EAAE,CAAC;YAChD,UAAU,CAAC,6BAA6B,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnE,OAAO;QACX,CAAC;QAED,MAAM,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC;QAC1C,QAAQ,CAAC,cAAc,CAAC,CAAC;QACzB,IAAI,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAG,YAAY,CAAC,sBAAsB,EAAE,CAAC;YACzD,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;gBAC1C,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;QACD,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,SAAS,sBAAsB,CAAC,CAAkB;QAC9C,IAAA,2BAAM,EAAC,CAAC,CAAC,CAAC,YAAY,4BAAO,CAAC,CAAC,CAAC;QAChC,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;QACjG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC3B,uBAAuB;QACvB,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YACpC,UAAU,CAAC,iCAAiC,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjE,CAAC;QACD,MAAM,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAsB,CAAC;QAC9E,uBAAuB;QACvB,IAAI,CAAC,CAAC;YAAE,OAAO;QACf,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IAED,SAAS,gBAAgB,CAAC,UAA0B;QAChD,IAAI,UAAU,CAAC,gBAAgB,EAAE,KAAK,iCAAQ,CAAC,eAAe,EAAE,CAAC;YAC7D,OAAO;QACX,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,UAAU;YAAE,OAAO;QACnC,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC;QAC5C,IAAI,CAAC,OAAO;YAAE,OAAO;QACrB,MAAM,KAAK,GAAgB,OAAO,CAAC,KAAK,CAAC;QACzC,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvB,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACJ,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IACD,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,MAAM,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YAChB,OAAO,EAAE,CAAC;YACV,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YACpE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,SAAS;YACb,CAAC;YACD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,SAAS;YACb,CAAC;YACD,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;gBACnC,SAAS;YACb,CAAC;YACD,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;YACjD,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,SAAS,KAAK,iCAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,KAAK,iCAAS,CAAC,YAAY,EAAE,CAAC;gBACrF,MAAM,cAAc,GAAI,IAAoC,CAAC,QAAQ,CAAC;gBACtE,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC,cAAc,CAAE,CAAC;gBAChE,IAAI,YAAY,EAAE,CAAC;oBACf,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC5C,CAAC;qBAAM,CAAC;oBACJ,uBAAuB;oBACvB,IAAI,cAAc,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC;wBAC5B,UAAU,CAAC,+BAA+B,EAAE,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC3E,CAAC;gBACL,CAAC;gBACD,MAAM,KAAK,GAAG,IAAsB,CAAC;gBACrC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC;YAED,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;YAC/E,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,SAAS;YACb,CAAC;YACD,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,SAAS;YACb,CAAC;YACD,MAAM,cAAc,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC;YACtD,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC5B,CAAC;IACL,CAAC;IAED,MAAM,MAAM,GAAG,EAAE,wBAAwB,EAAE,wBAAwB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;IACxF,IAAI,KAAK,EAAE,CAAC;QACR,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAgB,kCAAkC,CAC9C,SAAqB,EACrB,KAA4E;IAE5E,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;IAE5C,MAAM,EAAE,wBAAwB,EAAE,GAAG,iCAAiC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAEzF,MAAM,GAAG,GAAG,IAAI,GAAG,CAAS,wBAAwB,CAAC,CAAC;IACtD,MAAM,KAAK,GAAa,EAAE,CAAC;IAC3B,KAAK,MAAM,CAAC,IAAI,wBAAwB,EAAE,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACV,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACd,SAAS;QACb,CAAC;QACD,MAAM,UAAU,GAAG,iCAAiC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC1F,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,wBAAwB,EAAE,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnB,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAChB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;QACL,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,OAAO,EAAE,wBAAwB,EAAE,KAAK,EAAE,CAAC;AAC/C,CAAC;AAED,SAAgB,iDAAiD,CAC7D,SAAqB,EACrB,YAAsB,EACtB,KAA4E;IAG5E,MAAM,UAAU,GAAG,SAA6B,CAAC;IAEjD,MAAM,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAEnD,MAAM,wBAAwB,GAAG,kCAAkC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,wBAAwB,CAAC;IAC/G,MAAM,iBAAiB,GAAgB,IAAI,GAAG,CAAS,CAAC,GAAG,wBAAwB,CAAC,CAAC,CAAC;IAEtF,MAAM,QAAQ,GAAG,CAAC,aAAqB,EAAE,EAAE;QACvC,IAAI,aAAa,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7E,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACrC,wBAAwB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC;IACL,CAAC,CAAA;IAED,yDAAyD;IACzD,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC;QAC3C,MAAM,UAAU,GAAkB,IAAK,CAAC,aAAa,EAAE,CAAC;QACxD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACjC,qCAAqC;YACrC,kCAAkC;YAClC,oBAAoB;YACpB,MAAM,6BAA6B,GAAG,IAAA,iCAAgB,EAAC,SAAS,CAAE,CAAC,MAAM,CAAC,SAAS,CAAC;YACpF,IAAI,6BAA6B,KAAK,CAAC,IAAI,6BAA6B,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;gBAC3F,MAAM,WAAW,GAAG,YAAY,CAAC,6BAA6B,CAAC,CAAC;gBAChE,IAAI,WAAW,IAAI,YAAY,EAAE,CAAC;oBAC9B,QAAQ,CAAC,6BAA6B,CAAC,CAAC;gBAC5C,CAAC;YACL,CAAC;YACD,MAAM,0BAA0B,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;YAC9D,IAAI,0BAA0B,KAAK,CAAC,IAAI,0BAA0B,KAAK,SAAS,CAAC,KAAK,EAAE,CAAC;gBACrF,MAAM,WAAW,GAAG,YAAY,CAAC,0BAA0B,CAAC,CAAC;gBAC7D,IAAI,WAAW,IAAI,YAAY,EAAE,CAAC;oBAC9B,QAAQ,CAAC,0BAA0B,CAAC,CAAC;gBACzC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IACD,OAAO,wBAAwB,CAAC;AACpC,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,+BAA+B,CAAC,YAA2B;IACvE,iCAAiC;IACjC,gFAAgF;IAChF,wDAAwD;IACxD,2DAA2D;IAC3D,EAAE;IACF,iEAAiE;IACjE,mDAAmD;IACnD,EAAE;IACF,wFAAwF;IACxF,2EAA2E;IAC3E,sCAAsC;IACtC,MAAM,UAAU,GAAG,YAAY,CAAC,iBAAiB,EAAE,CAAC;IAEpD,MAAM,YAAY,GAAa,CAAC,CAAC,CAAC,CAAC;IAEnC,MAAM,GAAG,GAAG,IAAI,GAAG,EAA0F,CAAC;IAC9G,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,EAAE,OAAO,EAAE,wBAAwB,EAAE,GAAG,iCAAiC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpG,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClG,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;IAClC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEf,MAAM,CAAC,GAAG,CAAC,CAAa,EAAE,EAAE;QACxB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACrB,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI;YAAE,OAAO;QAClB,MAAM,EAAE,wBAAwB,EAAE,GAAG,IAAI,CAAC;QAC1C,KAAK,MAAM,CAAC,IAAI,wBAAwB,IAAI,EAAE,EAAE,CAAC;YAC7C,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;QACD,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC,CAAC;IAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC;QAChC,IAAI,OAAO,EAAE,CAAC;YACV,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;IACL,CAAC;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC;QAChC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC;IACL,CAAC;IAED,MAAM,aAAa,GAAa,EAAE,CAAC;IACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,MAAM,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QACvC,IAAA,2BAAM,EAAC,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,aAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC;AAC3C,CAAC;AAED,MAAM,OAAO,GAAG,KAAK,CAAC;AACtB,SAAgB,4BAA4B,CAAC,SAAqB,EAAE,aAAwB;IACxF,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;IAC5C,aAAa,GAAG,aAAa,IAAI,+BAA+B,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC;IAC7F,MAAM,wBAAwB,GAAG,iDAAiD,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IAC7G,OAAO,CAAC,GAAG,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC7F,CAAC;AAED;;GAEG;AACH,SAAgB,mCAAmC,CAAC,UAAwB,EAAE,iBAA6B;IACvG,MAAM,gBAAgB,GAAqB,IAAI,GAAG,EAAE,CAAC;IACrD,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzC,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,OAAO,gBAAgB,CAAC;QACxB,oFAAoF;IACxF,CAAC;IACD,IAAA,2BAAM,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,KAAK,8BAA8B,CAAC,CAAC;IAEtE,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IACrD,EAAE;IACF,IAAI,iBAAiB,EAAE,CAAC;QACpB,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IAC7D,CAAC;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,iBAAiB,IAAI,iBAAiB,KAAK,GAAG,EAAE,CAAC;YACjD,SAAS;QACb,CAAC;QACD,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IAC/C,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC5B,CAAC"}