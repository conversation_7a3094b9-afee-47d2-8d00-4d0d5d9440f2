{"version": 3, "file": "ua_non_exclusive_limit_alarm_impl.js", "sourceRoot": "", "sources": ["../../../src/alarms_and_conditions/ua_non_exclusive_limit_alarm_impl.ts"], "names": [], "mappings": ";;;AAAA,mCAAmC;AACnC;;GAEG;AACH,yDAA2C;AAE3C,mEAAqD;AAIrD,kFAA6F;AAI7F,+DAA0D;AAC1D,+DAAyD;AAMzD,MAAa,4BAA6B,SAAQ,sCAAgB;IACvD,MAAM,CAAC,WAAW,CACrB,SAA2B,EAC3B,IAAmC,EACnC,OAAqC,EACrC,IAAqC;QAErC,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;QAE5C,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAE5C,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE,CAAC;YAC/D,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC;YAC5D,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACnC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,CAAC;YAC7D,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE,CAAC;YACjE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC5C,CAAC;QACD,MAAM,qBAAqB,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE/D,0BAA0B;QAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,wCAAwC,GAAG,IAAI,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,0BAA0B,GAAG,YAAY,CAAC,aAAa,CAAC,4BAA4B,CAAC,CAAC;QAC5F,0BAA0B;QAC1B,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC9D,CAAC;QACD,iEAAiE;QAEjE,MAAM,KAAK,GAAG,sCAAgB,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAiC,CAAC;QAC3G,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,4BAA4B,CAAC,SAAS,CAAC,CAAC;QACrE,IAAA,0BAAM,EAAC,KAAK,YAAY,sCAAgB,CAAC,CAAC;QAC1C,IAAA,0BAAM,EAAC,KAAK,YAAY,4BAA4B,CAAC,CAAC;QAEtD,kCAAkC;QAClC,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACpB,IAAA,2DAAmC,EAAC,KAAK,CAAC,WAAW,EAAE;gBACnD,UAAU,EAAE,iBAAiB;gBAC7B,SAAS,EAAE,eAAe;aAC7B,CAAC,CAAC;YACH,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClC,IAAA,0BAAM,EAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAA,2DAAmC,EAAC,KAAK,CAAC,QAAQ,EAAE;gBAChD,UAAU,EAAE,cAAc;gBAC1B,SAAS,EAAE,YAAY;aAC1B,CAAC,CAAC;YACH,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAA,0BAAM,EAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YAClB,IAAA,2DAAmC,EAAC,KAAK,CAAC,SAAS,EAAE;gBACjD,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE,aAAa;aAC3B,CAAC,CAAC;YACH,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAChC,IAAA,0BAAM,EAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YACtB,IAAA,2DAAmC,EAAC,KAAK,CAAC,aAAa,EAAE;gBACrD,UAAU,EAAE,mBAAmB;gBAC/B,SAAS,EAAE,iBAAiB;aAC/B,CAAC,CAAC;YACH,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACpC,IAAA,0BAAM,EAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC;QACzE,CAAC;QAED,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAElC,KAAK,CAAC,WAAW,EAAE,CAAC;QAEpB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,uBAAuB,CAC1B,KAAoB,EACpB,QAAiB,EACjB,KAAa,EACb,gBAA+B;QAE/B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,IAAI,uCAAiB,CAAC;gBACzB,OAAO,EAAE,gBAAgB;gBACzB,OAAO,EAAE,oCAAW,CAAC,IAAI;gBACzB,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,CAAC;aACd,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,uCAAiB,CAAC;gBACzB,OAAO,EAAE,eAAe,GAAG,KAAK,GAAG,gBAAgB,GAAG,KAAK;gBAC3D,OAAO,EAAE,oCAAW,CAAC,IAAI;gBACzB,MAAM,EAAE,IAAI;gBACZ,QAAQ,EAAE,GAAG;aAChB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEM,oBAAoB,CAAC,MAAiC,EAAE,QAAiB,EAAE,KAAa;QAC3F,MAAM,KAAK,GAAG,IAAW,CAAC;QAE1B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,SAAS,QAAQ,CAAC,IAAY;YAC1B,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE,CAAC;gBAC3B,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YACjD,CAAC;QACL,CAAC;QAED,QAAQ,CAAC,UAAU,CAAC,CAAC;QACrB,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjB,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChB,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEnB,wBAAwB;QACxB,IAAI,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;aAC9B,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aAC1C,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC;aACzB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QAElB,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEnC,sCAAgB,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC1F,CAAC;IAES,0BAA0B,CAAC,KAAa;QAC9C,IAAA,0BAAM,EAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,8BAA8B,CAAC,CAAC;QAExD,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,MAAM,MAAM,GAAQ;YAChB,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO;YACtE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO;YAC1D,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO;YACvD,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO;SACnE,CAAC;QAEF,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,SAAS,IAAI,CAAC,SAAiB,EAAE,UAAyB;YACtD,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,OAAO,EAAE,CAAC;gBAChC,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;gBACzB,QAAQ,GAAG,QAAQ,IAAI,GAAG,CAAC;gBAC3B,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,EAAE,CAAC;oBAC5B,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;oBACxB,KAAK,IAAI,CAAC,CAAC;gBACf,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE;YAClB,OAAO,IAAI,CAAC,gBAAgB,EAAE,GAAG,KAAK,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;YACd,OAAO,IAAI,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;YACb,OAAO,IAAI,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC;QACtC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE;YAChB,OAAO,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACZ,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC;IACL,CAAC;CACJ;AArLD,oEAqLC"}