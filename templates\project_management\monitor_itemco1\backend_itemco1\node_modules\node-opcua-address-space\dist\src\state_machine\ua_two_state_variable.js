"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UATwoStateVariableImpl = void 0;
exports._install_TwoStateVariable_machinery = _install_TwoStateVariable_machinery;
exports.promoteToTwoStateVariable = promoteToTwoStateVariable;
exports._addTwoStateVariable = _addTwoStateVariable;
/**
* @module node-opcua-address-space
*/
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_constants_1 = require("node-opcua-constants");
const node_opcua_data_model_1 = require("node-opcua-data-model");
const node_opcua_nodeid_1 = require("node-opcua-nodeid");
const node_opcua_nodeid_2 = require("node-opcua-nodeid");
const node_opcua_status_code_1 = require("node-opcua-status-code");
const node_opcua_variant_1 = require("node-opcua-variant");
const node_opcua_variant_2 = require("node-opcua-variant");
// public interfaces
const register_node_promoter_1 = require("../../source/loader/register_node_promoter");
// private types
const ua_variable_impl_1 = require("../ua_variable_impl");
const hasTrueSubState_ReferenceTypeNodeId = (0, node_opcua_nodeid_1.resolveNodeId)("HasTrueSubState");
const hasFalseSubState_ReferenceTypeNodeId = (0, node_opcua_nodeid_1.resolveNodeId)("HasFalseSubState");
// Release 1.03 12 OPC Unified Architecture, Part 9
// Two-state state machines
// Most states defined in this standard are simple – i.e. they are either TRUE or FALSE. The
// TwoStateVariableType is introduced specifically for this use case. More complex states are
// modelled by using a StateMachineType defined in Part 5.
// The TwoStateVariableType is derived from the StateVariableType.
//
// Attribute        Value
// BrowseName       TwoStateVariableType
// DataType         LocalizedText
// ValueRank        -1 (-1 = Scalar)
// IsAbstract       False
//
// Subtype of the StateVariableType defined in Part 5.
// Note that a Reference to this subtype is not shown in the definition of the StateVariableType
//
// References      NodeClass BrowseName              DataType      TypeDefinition Modelling Rule
// HasProperty     Variable  Id                      Boolean       PropertyType   Mandatory
// HasProperty     Variable  TransitionTime          UtcTime       PropertyType   Optional
// HasProperty     Variable  EffectiveTransitionTime UtcTime       PropertyType   Optional
// HasProperty     Variable  TrueState               LocalizedText PropertyType   Optional
// HasProperty     Variable  FalseState              LocalizedText PropertyType   Optional
// HasTrueSubState StateMachine or
//                 TwoStateVariableType
//                                                  <StateIdentifier> Defined in Clause 5.4.2 Optional
// HasFalseSubState StateMachine or
//                  TwoStateVariableType
//                                                  <StateIdentifier> Defined in Clause 5.4.3 Optional
function _updateTransitionTime(node, _subState, options) {
    // TransitionTime specifies the time when the current state was entered.
    if (node.transitionTime) {
        const transitionTime = options?.transitionTime || new Date();
        node.transitionTime.setValueFromSource({ dataType: node_opcua_variant_2.DataType.DateTime, value: transitionTime });
    }
}
function _updateEffectiveTransitionTime(node, options) {
    if (node.effectiveTransitionTime) {
        const effectiveTransitionTime = options?.effectiveTransitionTime || new Date();
        // because subStateNode ",subStateNode.browseName.toString());
        node.effectiveTransitionTime.setValueFromSource({
            dataType: node_opcua_variant_2.DataType.DateTime,
            value: effectiveTransitionTime
        });
    }
}
function _getEffectiveDisplayName(node) {
    const humanReadableString = _getHumanReadableString(node);
    if (humanReadableString.statusCode.isNotGood()) {
        return humanReadableString;
    }
    const boolValue = node.getValue();
    let subStateNodes;
    if (boolValue) {
        subStateNodes = node.findReferencesExAsObject("HasTrueSubState", node_opcua_data_model_1.BrowseDirection.Forward);
    }
    else {
        subStateNodes = node.findReferencesExAsObject("HasFalseSubState", node_opcua_data_model_1.BrowseDirection.Forward);
    }
    const states = subStateNodes.forEach((n) => {
        // todo happen
    });
    return humanReadableString;
}
function _getHumanReadableString(node) {
    const dataValue = node.id.readValue();
    if (dataValue.statusCode.isNotGood()) {
        const _c = dataValue.clone();
        _c.value = new node_opcua_variant_1.Variant({
            dataType: node_opcua_variant_2.DataType.LocalizedText,
            value: (0, node_opcua_data_model_1.coerceLocalizedText)("")
        });
        return _c;
    }
    (0, node_opcua_assert_1.assert)(dataValue.value.dataType === node_opcua_variant_2.DataType.Boolean);
    const boolValue = dataValue.value.value;
    // The Value Attribute of a TwoStateVariable contains the current state as a human readable name.
    // The EnabledState for example, might contain the name “Enabled” when TRUE and “Disabled” when FALSE.
    const dataValue2 = dataValue.clone();
    dataValue2.value = new node_opcua_variant_1.Variant({
        dataType: node_opcua_variant_2.DataType.LocalizedText,
        value: boolValue ? node.getTrueState() : node.getFalseState()
    });
    return dataValue2;
}
function _install_TwoStateVariable_machinery(node, options) {
    (0, node_opcua_assert_1.assert)(node.dataTypeObj.browseName.toString() === "LocalizedText");
    (0, node_opcua_assert_1.assert)(node.minimumSamplingInterval === 0);
    (0, node_opcua_assert_1.assert)(node.typeDefinitionObj.browseName.toString() === "TwoStateVariableType");
    (0, node_opcua_assert_1.assert)(node.dataTypeObj.browseName.toString() === "LocalizedText");
    (0, node_opcua_assert_1.assert)(Object.prototype.hasOwnProperty.call(node, "valueRank") && (node.valueRank === -1 || node.valueRank === 0));
    // promote node into a UATwoStateVariable
    const _node = promoteToTwoStateVariable(node);
    node.initialize(options);
    return _node;
}
function promoteToTwoStateVariable(node) {
    if (node instanceof UATwoStateVariableImpl) {
        return node;
    }
    // istanbul ignore next
    if (!(node instanceof ua_variable_impl_1.UAVariableImpl)) {
        throw new Error("Trying to promote a invalid object");
    }
    Object.setPrototypeOf(node, UATwoStateVariableImpl.prototype);
    return node;
}
(0, register_node_promoter_1.registerNodePromoter)(node_opcua_constants_1.VariableTypeIds.TwoStateVariableType, promoteToTwoStateVariable);
/***
 * @class UATwoStateVariable
 */
class UATwoStateVariableImpl extends ua_variable_impl_1.UAVariableImplT {
    constructor(opts) {
        super(opts);
    }
    get isFalseSubStateOf() {
        return super.isFalseSubStateOf;
    }
    get isTrueSubStateOf() {
        return super.isTrueSubStateOf;
    }
    initialize(options) {
        if (options.trueState) {
            (0, node_opcua_assert_1.assert)(!!options.falseState);
            (0, node_opcua_assert_1.assert)(typeof options.trueState === "string");
            (0, node_opcua_assert_1.assert)(typeof options.falseState === "string");
            if (this.falseState) {
                this.falseState.setValueFromSource({
                    dataType: node_opcua_variant_2.DataType.LocalizedText,
                    value: (0, node_opcua_data_model_1.coerceLocalizedText)(options.falseState)
                });
            }
            else {
                this._trueState = (0, node_opcua_data_model_1.coerceLocalizedText)(options.trueState).text;
            }
            if (this.trueState) {
                this.trueState.setValueFromSource({
                    dataType: node_opcua_variant_2.DataType.LocalizedText,
                    value: (0, node_opcua_data_model_1.coerceLocalizedText)(options.trueState)
                });
            }
            else {
                this._falseState = (0, node_opcua_data_model_1.coerceLocalizedText)(options.falseState).text;
            }
        }
        // handle isTrueSubStateOf
        if (options.isTrueSubStateOf) {
            this.addReference({
                isForward: false,
                nodeId: options.isTrueSubStateOf,
                referenceType: "HasTrueSubState"
            });
        }
        if (options.isFalseSubStateOf) {
            this.addReference({
                isForward: false,
                nodeId: options.isFalseSubStateOf,
                referenceType: "HasFalseSubState"
            });
        }
        if (options.value === undefined) {
            this.id.setValueFromSource({
                dataType: "Boolean",
                value: false
            }, node_opcua_status_code_1.StatusCodes.UncertainInitialValue);
        }
        else if (typeof options.value === "boolean") {
            this.id.setValueFromSource({
                dataType: "Boolean",
                value: options.value
            }, node_opcua_status_code_1.StatusCodes.Good);
        }
        else if (Object.prototype.hasOwnProperty.call(options.value, "dataType")) {
            (0, node_opcua_assert_1.assert)(options.value.dataType === node_opcua_variant_2.DataType.Boolean);
            this.id.setValueFromSource(options.value, node_opcua_status_code_1.StatusCodes.Good);
        }
        else {
            this.id.bindVariable(options.value);
        }
        this._postInitialize();
    }
    _postInitialize() {
        if (this.effectiveTransitionTime) {
            // install "value_changed" event handler on SubState that are already defined
            const subStates = this.getTrueSubStates().concat(this.getFalseSubStates());
            for (const subState of subStates) {
                subState.on("value_changed", () => _updateEffectiveTransitionTime(this));
            }
        }
        // it should be possible to define a trueState and falseState LocalizedText even if
        // the trueState or FalseState node is not exposed. Therefore we need to store their value
        // into dedicated variables.
        this.id.on("value_changed", () => {
            this._internal_set_dataValue(_getHumanReadableString(this));
        });
        this._internal_set_dataValue(_getHumanReadableString(this));
        // todo : also set the effectiveDisplayName if present
        // from spec Part 5
        // Release 1.03 OPC Unified Architecture, Part 5
        // EffectiveDisplayName contains a human readable name for the current state of the state
        // machine after taking the state of any SubStateMachines in account. There is no rule specified
        // for which state or sub-state should be used. It is up to the Server and will depend on the
        // semantics of the StateMachineType
        //
        // EffectiveDisplayName will be constructed by adding the EnabledState
        // and the State of the addTrue state
        if (this.effectiveDisplayName) {
            this.id.on("value_changed", () => {
                this.effectiveDisplayName._internal_set_dataValue(_getEffectiveDisplayName(this));
            });
            this.effectiveDisplayName._internal_set_dataValue(_getEffectiveDisplayName(this));
        }
    }
    /**
     */
    setValue(boolValue, options) {
        (0, node_opcua_assert_1.assert)(typeof boolValue === "boolean");
        const dataValue = this.id.readValue();
        const oldValue = dataValue.value.value;
        if (dataValue.statusCode.isGood() && boolValue === oldValue) {
            return; // nothing to do
        }
        //
        this.id.setValueFromSource(new node_opcua_variant_1.Variant({ dataType: node_opcua_variant_2.DataType.Boolean, value: boolValue }));
        _updateTransitionTime(this, undefined, options);
        _updateEffectiveTransitionTime(this, options);
    }
    /**
     */
    getValue() {
        const dataValue = this.id.readValue();
        (0, node_opcua_assert_1.assert)(dataValue.statusCode.isGood());
        (0, node_opcua_assert_1.assert)(dataValue.value.dataType === node_opcua_variant_2.DataType.Boolean);
        return dataValue.value.value;
    }
    /**
     */
    getValueAsString() {
        const dataValue = this.readValue();
        (0, node_opcua_assert_1.assert)(dataValue.statusCode.isGood());
        (0, node_opcua_assert_1.assert)(dataValue.value.dataType === node_opcua_variant_2.DataType.LocalizedText);
        return dataValue.value.value.text?.toString() || "";
    }
    getTrueState() {
        return this.trueState ? this.trueState.readValue().value.value : (0, node_opcua_data_model_1.coerceLocalizedText)(this._trueState || "TRUE");
    }
    getFalseState() {
        return this.falseState ? this.falseState.readValue().value.value : (0, node_opcua_data_model_1.coerceLocalizedText)(this._falseState || "FALSE");
    }
    // TODO : shall we care about overloading the remove_backward_reference method ?
    // some TrueSubState and FalseSubState relationship may be added later
    // so we need a mechanism to keep adding the "value_changed" event handle on subStates that
    // will be defined later.
    // install change detection on sub State
    // this is useful to change the effective transitionTime
    // EffectiveTransitionTime specifies the time when the current state or one of its sub states was entered.
    // If, for example, a LevelAlarm is active and – while active – switches several times between High and
    // HighHigh, then the TransitionTime stays at the point in time where the Alarm became active whereas the
    // EffectiveTransitionTime changes with each shift of a sub state.
    _add_backward_reference(reference) {
        super._add_backward_reference(reference);
        if (reference.isForward &&
            ((0, node_opcua_nodeid_2.sameNodeId)(reference.referenceType, hasTrueSubState_ReferenceTypeNodeId) ||
                (0, node_opcua_nodeid_2.sameNodeId)(reference.referenceType, hasFalseSubState_ReferenceTypeNodeId))) {
            const addressSpace = this.addressSpace;
            // add event handle
            const subState = addressSpace.findNode(reference.nodeId);
            subState.on("value_changed", _updateEffectiveTransitionTime.bind(null, this, undefined));
        }
    }
}
exports.UATwoStateVariableImpl = UATwoStateVariableImpl;
function _addTwoStateVariable(namespace, options) {
    const addressSpace = namespace.addressSpace;
    const twoStateVariableType = addressSpace.findVariableType("TwoStateVariableType");
    if (!twoStateVariableType) {
        throw new Error("cannot find TwoStateVariableType");
    }
    options.optionals = options.optionals || [];
    if (options.trueState) {
        options.optionals.push("TrueState");
    }
    if (options.falseState) {
        options.optionals.push("FalseState");
    }
    // we want event based changes...
    options.minimumSamplingInterval = 0;
    const node = twoStateVariableType.instantiate({
        browseName: options.browseName,
        nodeId: options.nodeId,
        description: options.description,
        componentOf: options.componentOf,
        organizedBy: options.organizedBy,
        modellingRule: options.modellingRule,
        dataType: (0, node_opcua_nodeid_1.resolveNodeId)(node_opcua_variant_2.DataType.LocalizedText),
        minimumSamplingInterval: options.minimumSamplingInterval,
        optionals: options.optionals
    });
    const _node = _install_TwoStateVariable_machinery(node, options);
    return _node;
}
//# sourceMappingURL=ua_two_state_variable.js.map