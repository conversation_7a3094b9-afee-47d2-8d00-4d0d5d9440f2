const mqtt = require("mqtt");
const WebSocket = require("ws");

// <PERSON>ấu hình MQTT
const mqttBrokerUrl = "mqtt://broker.hivemq.com"; // hoặc broker của bạn
const mqttTopic = "plc/tben02/#"; // Lắng nghe tất cả các tag của thiết bị tben01

// Biến lưu dữ liệu mới nhất
let latestData = {};

// Kết nối MQTT
const mqttClient = mqtt.connect(mqttBrokerUrl);

mqttClient.on("connect", () => {
  console.log("✅ MQTT connected");
  mqttClient.subscribe(mqttTopic, (err) => {
    if (err) {
      console.error("❌ MQTT subscribe error:", err);
    } else {
      console.log(`📡 Subscribed to MQTT topic: ${mqttTopic}`);
    }
  });
});

// Nhận dữ liệu MQTT
mqttClient.on("message", (topic, message) => {
  try {
    const parsed = JSON.parse(message.toString());

    console.log(`📥 MQTT [${topic}]:`);

    if (Array.isArray(parsed)) {
      parsed.forEach((item) => {
        if (item.tag && item.value !== undefined) {
          latestData[item.tag] = item.value;
          console.log(`  🏷️ ${item.tag} = ${item.value}`);
        }
      });
    } else if (parsed.tag && parsed.value !== undefined) {
      latestData[parsed.tag] = parsed.value;
      console.log(`  🏷️ ${parsed.tag} = ${parsed.value}`);
    }

    // Gửi đến tất cả client WebSocket
    const payload = JSON.stringify(latestData);
    wss.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(payload);
      }
    });
  } catch (err) {
    console.error("❌ Error parsing MQTT message:", err.message);
  }
});

// WebSocket Server
const wss = new WebSocket.Server({ port: 3000 }, () => {
  console.log("🚀 WebSocket Server chạy tại http://localhost:3001");
});

// Khi client WebSocket kết nối
wss.on("connection", (ws) => {
  console.log("🧩 WebSocket client mới đã kết nối");

  // Gửi dữ liệu hiện tại khi client mới vào
  ws.send(JSON.stringify(latestData));
});
