<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com"></script>
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<?php
$project_name = isset($_GET['project']) ? $_GET['project'] : 'Unknown Project';
$company_name = $_SESSION['user_type'] === 'admin' ? $_SESSION['selected_company'] : $_SESSION['company_name'];
$is_admin = $_SESSION['user_type'] === 'admin';
?>

<style>
    /* Fast pulse animation for warning icon */
    @keyframes fastPulse {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.5;
            transform: scale(1.1);
        }
    }

    .fast-pulse {
        animation: fastPulse 0.5s infinite;
    }

    /* Hose reel image alternation */

    .hose-reel-container {
        position: relative;
        width: 200px;
        height: 200px;
        margin: 0 auto;
        margin-left: 53px;
    }

    .hose-reel-image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        transition: opacity 0.3s ease;
    }

    .direction-icon {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 20px;
        height: 20px;
        background: #ef4444;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .step-item {
        color: white;
        padding: 8px 16px;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        font-size: 14px;
        border-radius: 4px;
    }

    .alert-item {
        color: black;
        padding: 8px 16px;
        margin-bottom: 6px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        border-radius: 4px;
    }

    .alert-icon {
        color: white;
        padding: 0 10px;
        font-weight: bold;
        border-radius: 4px;
    }
</style>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-yellow-100">
    <!-- Header -->
    <div class="bg-[#27613b]  px-4 py-3 rounded-xl max-w-[970px] mx-auto flex justify-between items-center">
        <!-- Bên trái -->
        <div class="flex items-center space-x-4">
            <?php if ($is_admin) { ?>
                <a href="quan-ly-du-an" class="w-10 h-10 bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition" title="Về Dashboard">
                    <i class="fas fa-home text-white"></i>
                </a>
            <?php } else { ?>
                <button onclick="history.back()" class="w-10 h-10 rounded-full flex items-center justify-center hover:bg-gray-200 transition">
                    <i class="fas fa-arrow-left text-white"></i>
                </button>
            <?php } ?>
            <span class="text-2xl font-bold text-white"><?= strtoupper($company_name) ?> - Project <?= $project_name ?></span>
        </div>

        <!-- Bên phải -->
        <div class="flex items-center space-x-4">
            <div id="datetime" class="text-lg font-bold text-white">--/--/-- --:--:--</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-4 pb-0">
        <div id="main-content" class="transition-all duration-300 max-w-[1000px] mx-auto px-4">
            <!-- Nội dung mặc định là OPERATION -->
            <div id="operation-panel">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 h-[424px]">
                    <!-- Left Panel -->
                    <div class="bg-white border-2 border-gray-400 rounded-xl p-3 shadow h-[424px] overflow-hidden flex flex-col">
                        <div class="bg-gray-300 p-2 text-center font-bold border-2 border-gray-400 mb-2 rounded">
                            Confirm delivery steps
                        </div>
                        <div class="flex-1 overflow-y-auto">
                            <div class="step-item bg-blue-700">Parking brake set</div>
                            <div class="step-item bg-blue-700">Ticket printer ready</div>
                            <div class="step-item bg-blue-700">PTO Switch on</div>
                            <div class="step-item bg-blue-700">Connect bonding cab</div>
                            <div class="step-item bg-blue-700">Meter permission</div>
                            <div class="step-item bg-blue-700">Open butterfly valve</div>
                            <div class="step-item bg-blue-700">One outlet only</div>
                            <div class="step-item bg-blue-700">Throttle high or low</div>
                        </div>
                    </div>

                    <!-- Middle Panel -->
                    <div class="bg-white border-2 border-gray-400 rounded-xl p-3 shadow h-[424px] overflow-hidden flex flex-col">
                        <div class="bg-gray-300 p-2 text-center font-bold border-2 border-gray-400 mb-2 rounded">
                            Stop delivery signals
                        </div>
                        <div class="flex-1 overflow-y-auto">
                            <div id="dpLight" class="alert-item bg-gray-300"><span>Filter DP high</span><span class="alert-icon bg-blue-700">!</span></div>
                            <div id="waterLight" class="alert-item bg-gray-300"><span>Water in filter</span><span class="alert-icon bg-blue-700">!</span></div>
                            <div id="truckShutdown" class="alert-item bg-gray-300"><span>Emergency active</span><span class="alert-icon bg-blue-700">!</span></div>
                            <div id="deadmanOvertime" class="alert-item bg-gray-300"><span>Deadman over time</span><span class="alert-icon bg-blue-700">!</span></div>
                            <div id="overrideLight" class="alert-item bg-gray-300"><span>Override active</span><span class="alert-icon bg-blue-700">!</span></div>
                        </div>

                        <!-- Warning Icon ở dưới -->
                        <div class="mt-2 flex justify-center">
                            <div id="stopSignalWarning" class="hidden">
                                <div class="flex items-center justify-center w-16 h-16 bg-red-500 rounded-full fast-pulse">
                                    <span class="text-white text-5xl font-bold">!</span>
                                </div>
                            </div>
                        </div>
                        <div id="stopSignalText" class="hidden text-center text-red-600 font-bold text-sm mt-1">STOP SIGNAL ACTIVE</div>
                    </div>

                    <!-- Right Panel -->
                    <div class="bg-white border-2 border-gray-400 rounded-xl p-3 shadow h-[424px] overflow-hidden flex flex-col">
                        <div class="bg-gray-300 p-2 text-center font-bold border-2 border-gray-400 mb-2 rounded">
                            Control panel
                        </div>
                        <div class="flex-1 overflow-y-auto">
                            <div class="flex gap-2 mb-2">
                                <div class="flex-1 bg-gray-300 text-black px-2 py-1 text-sm font-bold rounded">RPM</div>
                                <div id="rpmStatus" class="flex-1 bg-blue-700 text-white px-2 py-1 text-sm font-bold text-center rounded">High</div>
                            </div>
                            <div class="flex gap-2 mb-2">
                                <div class="flex-1 bg-gray-300 text-black px-2 py-1 text-sm font-bold rounded">FDF</div>
                                <div id="fdfStatus" class="flex-1 bg-blue-700 text-white px-2 py-1 text-sm font-bold text-center rounded">Fuel</div>
                            </div>
                            <div class="flex gap-2 mb-2">
                                <div class="flex-1 bg-gray-300 text-black px-2 py-1 text-sm font-bold rounded">Hose reel</div>
                                <div id="hoseReelStatus" class="flex-1 bg-blue-700 text-white px-2 py-1 text-sm font-bold text-center rounded">OFF</div>
                            </div>

                            <!-- Hose Reel Animation Area -->
                            <div class="mt-4 flex flex-col items-center">
                                <div id="hoseReelContainer" class="hose-reel-container hidden">
                                    <img id="hoseReelImage1" src="templates/project_management/monitor_itemco1/img/imageHoseReef1.png" alt="Hose Reel 1" class="hose-reel-image absolute inset-0  object-contain">
                                    <img id="hoseReelImage2" src="templates/project_management/monitor_itemco1/img/imageHoseReef2.png" alt="Hose Reel 2" class="hose-reel-image absolute inset-0  object-contain" style="opacity: 0;">
                                </div>
                                <div id="hoseReelLabel" class=" text-red-600 font-bold text-sm mt-2 hidden">HOSE REEL ACTIVE</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Panel khác -->
            <div id="monitor-panel" class="hidden">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 h-[424px]">
                    <!-- Gauge 1: Pipe Pressure -->
                    <div class="bg-white rounded-xl shadow border border-gray-300 p-4 text-center h-[424px] flex flex-col justify-between overflow-hidden">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-tachometer-alt text-2xl text-blue-600"></i>
                            </div>
                            <div class="text-xl font-bold text-gray-600">Pipe Pressure</div>
                        </div>
                        <div class="flex-1 relative">
                            <div class="absolute inset-0 flex flex-col items-center justify-center">
                                <div class="w-32 h-32 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                                    <i class="fas fa-water text-4xl text-white"></i>
                                </div>
                                <div id="pipePressure" class="text-3xl font-bold text-blue-600">-- bar</div>
                            </div>
                        </div>
                    </div>

                    <!-- Gauge 2: Fuel Delivered -->
                    <div class="bg-white rounded-xl shadow border border-gray-300 p-4 text-center h-[424px] flex flex-col justify-between overflow-hidden">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-gas-pump text-2xl text-green-600"></i>
                            </div>
                            <div class="text-xl font-bold text-gray-600">Fuel Delivered</div>
                        </div>
                        <div class="flex-1 relative">
                            <div class="absolute inset-0 flex flex-col items-center justify-center">
                                <div class="w-32 h-32 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                                    <i class="fas fa-oil-can text-4xl text-white"></i>
                                </div>
                                <div id="gauge-fuel" class="text-3xl font-bold text-green-600">-- Lits</div>
                            </div>
                        </div>
                    </div>

                    <!-- Gauge 3: Tank Level -->
                    <div class="bg-white rounded-xl shadow border border-gray-300 p-4 text-center h-[424px] flex flex-col justify-between overflow-hidden">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-battery-half text-2xl text-yellow-600"></i>
                            </div>
                            <div class="text-xl font-bold text-gray-600">Tank Level</div>
                        </div>
                        <div class="flex-1 relative">
                            <div class="absolute inset-0 flex flex-col items-center justify-center">
                                <div class="w-32 h-32 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mb-4 shadow-lg relative">
                                    <i class="fas fa-database text-4xl text-white"></i>
                                    <!-- Tank level indicator -->
                                    <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2">
                                        <div class="w-8 h-1 bg-white rounded-full opacity-80"></div>
                                    </div>
                                </div>
                                <div id="tankLevel" class="text-3xl font-bold text-yellow-600">-- %</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="diagnostic-panel" class="hidden">
                <!-- Diagnostic Content -->
                <div class="bg-white border-2 border-gray-400 rounded p-4 shadow h-[424px] overflow-hidden flex flex-col">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-bold text-gray-700">Diagnostic</h2>
                        <select id="diagnosticSelect" onchange="handleSelectChange()"
                            class=" bg-green-500 rounded px-2 py-1 text-lg text-white font-bold">
                            <option value="input">Input</option>
                            <option value="output">Output</option>
                            <option value="alarm">Alarm</option>
                        </select>
                    </div>

                    <!-- Input Section -->
                    <div id="inputSection" class="grid grid-cols-2 md:grid-cols-4 gap-3 flex-1 overflow-y-auto">
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Switch Override</span>
                            <div class="w-4 h-4 rounded-full bg-blue-600"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Cabinet Door</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">PS Parking Brake</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Platform Door</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Joystick Up</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Joystick Down</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Upper Limit</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Down Limit</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Conner Sensor</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Interlock Nozzle</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Overfill sensor</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Ground Interlock</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Water In Filter</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">DP Sensor</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Emergency</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Deadman</span>
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                        </div>
                    </div>

                    <!-- Output Section -->
                    <div id="outputSection" class="grid grid-cols-2 md:grid-cols-4 gap-3 hidden flex-1 overflow-y-auto">
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Parking Brake Valve</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Interlock Light</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Override Light</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Overfill Buzzer</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Hose Reel Wind</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Hose Reel Rewind</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Deadman Light</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">PTO, Pump Light</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">DP Light</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                        <div class="flex items-center justify-between bg-gray-100 px-3 py-2 h-20 rounded shadow">
                            <span class="text-sl font-medium">Water Light</span>
                            <div class="w-4 h-4 rounded-full bg-blue-700"></div>
                        </div>
                    </div>
                    <!-- Alarm Section -->
                    <div id="alarmSection" class="hidden p-4 bg-white rounded-xl shadow-lg flex-1 overflow-y-auto">
                        <div class="relative mb-4">
                            <!-- Tiêu đề căn giữa -->
                            <h2 class="text-2xl font-bold text-center text-red-600">Alarm History Table</h2>
                            <!-- Nút tải PDF ở góc phải -->
                            <button onclick="downloadPDF()" class="absolute right-0 top-1/2 -translate-y-1/2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-semibold py-2 px-4 rounded shadow-md">
                                📄PDF
                            </button>
                        </div>
                        <!-- Bộ lọc -->
                        <div class="mb-4 flex flex-col lg:flex-row items-center justify-between gap-4">
                            <label class="flex-1">
                                <span class="block mb-1 font-semibold">Alarm name:</span>
                                <input type="text" id="nameFilter" placeholder="Enter the alarm name..." oninput="filterAlarmTable()" class="border-2 border-gray-300 rounded px-3 py-2 w-full" />
                            </label>
                            <label class="flex-1">
                                <span class="block mb-1 font-semibold">From:</span>
                                <input type="datetime-local" id="fromTime" onchange="filterAlarmByTime()" class="border-2 border-gray-300 rounded px-3 py-2 w-full" />
                            </label>
                            <label class="flex-1">
                                <span class="block mb-1 font-semibold">To:</span>
                                <input type="datetime-local" id="toTime" onchange="filterAlarmByTime()" class="border-2 border-gray-300 rounded px-3 py-2 w-full" />
                            </label>
                        </div>
                        <!-- Bảng alarm -->
                        <div class="overflow-x-auto overflow-y-auto max-h-64">
                            <table id="alarmTable" class="min-w-full border border-gray-300 rounded overflow-hidden">
                                <thead class="bg-green-500 text-white font-bold text-sm">
                                    <tr>
                                        <th class="px-4 py-2 border-b border-gray-300 text-left">Timestamp</th>
                                        <th class="px-4 py-2 border-b border-gray-300 text-left">Name</th>
                                        <th class="px-4 py-2 border-b border-gray-300 text-left">Value</th>
                                        <th class="px-4 py-2 border-b border-gray-300 text-left">Description</th>
                                    </tr>
                                </thead>
                                <tbody id="alarmTableBody" class="text-sm">
                                    <!-- Dữ liệu sẽ được thêm qua JS -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div id="maintenance-panel" class="hidden">
                <div class="overflow-x-auto shadow-lg rounded-lg h-[424px]">
                    <table class="table-auto w-full h-full border border-gray-300 bg-white">
                        <thead class="bg-blue-700 text-white text-center">
                            <tr class="h-[80px]">
                                <th class="px-4 py-2 w-[50px] border">
                                    <i class="fas fa-list-ol text-lg"></i><br>No.
                                </th>
                                <th class="px-4 py-2 w-[100px] border">
                                    <i class="fas fa-tools text-lg"></i><br>Description
                                </th>
                                <th class="px-4 py-2 w-[200px] border">
                                    <i class="fas fa-clipboard-list text-lg"></i><br>Specification
                                </th>
                                <th class="px-4 py-2 border">
                                    <i class="fas fa-calendar-alt text-lg"></i><br>Time line
                                </th>
                                <th class="px-4 py-2 w-[100px] border">
                                    <i class="fas fa-check-circle text-lg"></i><br>Confirm
                                </th>
                            </tr>
                        </thead>
                        <tbody class="text-center text-gray-700">
                            <tr class="h-[80px]">
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-filter text-blue-600"></i>
                                        </div>
                                        1
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <i class="fas fa-filter text-lg text-blue-600 mr-2"></i>
                                        <span class="font-semibold">Filter</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <i class="fas fa-cog text-gray-500 mr-2"></i>
                                        <span>---</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <table class="w-full table-auto text-sm">
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Last ins/rep date: </td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate1"></span></td>
                                            <td class="px-1 py-1 w-2/5">Unit: Day</td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Next ins date:</td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate2"></span> </td>
                                            <td class="px-1 py-1 w-2/5" id="remaining1">Remaining: </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Next rep date:</td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate3"></span></td>
                                            <td class="px-1 py-1 w-2/5" id="remaining2">Remaining:</td>
                                        </tr>
                                    </table>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-check text-white text-xs"></i>
                                        </div>
                                        <span class="text-green-600 font-semibold">Working</span>
                                    </div>
                                </td>
                            </tr>
                            <tr class="h-[80px]">
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-database text-yellow-600"></i>
                                        </div>
                                        2
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <i class="fas fa-database text-lg text-yellow-600 mr-2"></i>
                                        <span class="font-semibold">Tank</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <i class="fas fa-cog text-gray-500 mr-2"></i>
                                        <span>---</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <table class="w-full table-auto text-sm">
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Last ins/rep date: </td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate4"></span></td>
                                            <td class="px-1 py-1 w-2/5">Unit: Day</td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Next ins date:</td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate5"></span> </td>
                                            <td class="px-1 py-1 w-2/5" id="remaining3">Remaining: </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Next rep date:</td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate6"></span></td>
                                            <td class="px-1 py-1 w-2/5" id="remaining4">Remaining:</td>
                                        </tr>
                                    </table>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-check text-white text-xs"></i>
                                        </div>
                                        <span class="text-green-600 font-semibold">Working</span>
                                    </div>
                                </td>
                            </tr>
                            <tr class="h-[80px]">
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-truck text-red-600"></i>
                                        </div>
                                        3
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <i class="fas fa-truck text-lg text-red-600 mr-2"></i>
                                        <span class="font-semibold">Truck</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <i class="fas fa-cog text-gray-500 mr-2"></i>
                                        <span>---</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <table class="w-full table-auto text-sm">
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Last main date: </td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate7"></span></td>
                                            <td class="px-1 py-1 w-2/5"></td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Type of last main:</td>
                                            <td class="px-1 py-1 w-1/5">
                                                <select class=" bg-green-500 rounded px-1 py-1 text-white text-left">
                                                    <option value="250">250</option>
                                                    <option value="500">500</option>
                                                    <option value="1000">1000</option>
                                                    <option value="2000">2000</option>
                                                    <option value="4000">4000</option>
                                                </select>
                                            </td>
                                            <td class="px-1 py-1 w-2/5"></td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Next main remaining:</td>
                                            <td class="px-1 py-1 w-1/5">20</td>
                                            <td class="px-1 py-1 w-2/5"></td>
                                        </tr>
                                    </table>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-check text-white text-xs"></i>
                                        </div>
                                        <span class="text-green-600 font-semibold">Working</span>
                                    </div>
                                </td>
                            </tr>
                            <tr class="h-[80px]">
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-water text-purple-600"></i>
                                        </div>
                                        4
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                            <i class="fas fa-water text-purple-600 mr-2"></i>
                                        <span class="font-semibold">Pipe</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <i class="fas fa-cog text-gray-500 mr-2"></i>
                                        <span>---</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 border">
                                    <table class="w-full table-auto text-sm">
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Last ins/rep date: </td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate8"></span></td>
                                            <td class="px-1 py-1 w-2/5">Unit: Day</td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Quar ins date:</td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate9"></span> </td>
                                            <td class="px-1 py-1 w-2/5">Remaining: </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 py-1 w-2/5">Expiry date:</td>
                                            <td class="px-1 py-1 w-1/5"><span id="currentDate10"></span></td>
                                            <td class="px-1 py-1 w-2/5">Remaining:</td>
                                        </tr>
                                    </table>
                                </td>
                                <td class="px-4 py-2 border">
                                    <div class="flex items-center justify-center">
                                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-check text-white text-xs"></i>
                                        </div>
                                        <span class="text-green-600 font-semibold">Working</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Bottom Buttons -->
        <div class="max-w-[1000px] mx-auto px-4 pt-4">
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-3">
                <button id="btn-operation" onclick="showPanel('operation')" class="active-tab bg-[#27613b] text-white p-4 rounded font-bold text-sm flex flex-col items-center justify-center">
                    <i class="fas fa-cog text-lg mb-1"></i> OPERATION
                </button>
                <button id="btn-monitor" onclick="showPanel('monitor')" class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded font-bold text-sm flex flex-col items-center justify-center">
                    <i class="fas fa-chart-line text-lg mb-1"></i> MONITOR
                </button>
                <button id="btn-diagnostic" onclick="showPanel('diagnostic')" class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded font-bold text-sm flex flex-col items-center justify-center">
                    <i class="fas fa-wrench text-lg mb-1"></i> DIAGNOSTIC
                </button>
                <button id="btn-maintenance" onclick="showPanel('maintenance')" class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded font-bold text-sm flex flex-col items-center justify-center">
                    <i class="fas fa-clipboard-list text-lg mb-1"></i> MAINTENANCE
                </button>
            </div>
        </div>
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
<script>
    function updateDateTime() {
        const now = new Date();
        document.getElementById('datetime').textContent = now.toLocaleDateString('en-GB') + ' - ' + now.toLocaleTimeString();
    }
    setInterval(updateDateTime, 1000);
    updateDateTime();

    function showPanel(panel) {
        const panels = ['operation', 'monitor', 'diagnostic', 'maintenance'];
        panels.forEach(p => {
            document.getElementById(`${p}-panel`).classList.add('hidden');
            document.getElementById(`btn-${p}`).classList.remove('bg-[#27613b]');
            document.getElementById(`btn-${p}`).classList.add('bg-blue-600');
        });

        document.getElementById(`${panel}-panel`).classList.remove('hidden');
        const btn = document.getElementById(`btn-${panel}`);
        btn.classList.remove('bg-blue-600');
        btn.classList.add('bg-[#27613b]');
    }

    function handleSelectChange() {
        const selected = document.getElementById('diagnosticSelect').value;
        document.getElementById('inputSection').style.display = selected === 'input' ? 'grid' : 'none';
        document.getElementById('outputSection').style.display = selected === 'output' ? 'grid' : 'none';
        document.getElementById('alarmSection').style.display = selected === 'alarm' ? 'block' : 'none';
    }

    function filterAlarmTable() {
        const filter = document.getElementById("nameFilter").value.toLowerCase();
        const rows = document.querySelectorAll("#alarmTableBody tr");
        rows.forEach(row => {
            const name = row.cells[1].textContent.toLowerCase();
            row.style.display = name.includes(filter) ? "" : "none";
        });
    }
    // confirm delivery step
    const stepTags = ["hmiParkingBrake", "hmiTicketPrinterReady", "hmiPTO", "hmiConnectBondingCab", "hmiMeterSignal", "hmiOpenButterflyValve", "oneOutletOnly", "hmiRPM"];

    function updateStepColorsByData(data) {
        const stepItems = document.querySelectorAll(".step-item");
        stepTags.forEach((tag, index) => {
            const el = stepItems[index];
            if (!el) return;
            const value = data[tag];
            // Xóa tất cả màu nền cũ
            el.classList.remove("bg-red-500", "bg-blue-700");
            // Thêm màu mới theo trạng thái
            if (value === true || value === 1 || value === "true") {
                el.classList.add("bg-red-500");
            } else {
                el.classList.add("bg-blue-700");
            }
        });
    }
    // stop delivery signal
    const stopDeliverySignal = ["dpLight", "waterLight", "truckShutdown", "deadmanOvertime", "overrideLight"];

    function updateStopSignalByData(data) {
        const stopItems = document.querySelectorAll(".alert-icon");
        let hasActiveSignal = false; // Biến để check có signal nào active không

        stopDeliverySignal.forEach((tag, index) => {
            const el = stopItems[index];
            if (!el) return;
            const value = data[tag];
            // Xóa tất cả màu nền cũ
            el.classList.remove("bg-red-500", "bg-blue-700");
            // Thêm màu mới theo trạng thái
            if (value === true || value === 1 || value === "true") {
                el.classList.add("bg-red-500");
                hasActiveSignal = true; // Có ít nhất 1 signal active
            } else {
                el.classList.add("bg-blue-700");
            }
        });

        // Hiển thị/ẩn warning icon dựa trên hasActiveSignal
        const warningIcon = document.getElementById("stopSignalWarning");
        if (warningIcon) {
            if (hasActiveSignal) {
                warningIcon.classList.remove("hidden");
            } else {
                warningIcon.classList.add("hidden");
            }
        }
        const stopSignalText = document.getElementById("stopSignalText");
        if (stopSignalText) {
            if (hasActiveSignal) {
                stopSignalText.classList.remove("hidden");
            } else {
                stopSignalText.classList.add("hidden");
            }
        }
    }
    // control panel
    function updateControlPanelByData(data) {
        const rpmEl = document.getElementById("rpmStatus");
        const fdfEl = document.getElementById("fdfStatus");
        const hoseEl = document.getElementById("hoseReelStatus");
        // Bảo vệ: nếu 1 trong 3 phần tử không tồn tại thì bỏ qua
        if (!rpmEl || !fdfEl || !hoseEl) return;
        // --- RPM ---
        rpmEl.classList.remove("bg-red-500", "bg-blue-700");
        if (data.hmiRPM === 1 || data.hmiRPM === true) {
            rpmEl.textContent = "High";
            rpmEl.classList.add("bg-red-500");
        } else {
            rpmEl.textContent = "Low";
            rpmEl.classList.add("bg-blue-700");
        }
        // --- Fuel/Defuel ---
        fdfEl.classList.remove("bg-red-500", "bg-blue-700");
        if (data.fuelDefuel === 1 || data.fuelDefuel === true) {
            fdfEl.textContent = "Defuel";
            fdfEl.classList.add("bg-red-500");
        } else {
            fdfEl.textContent = "Fuel";
            fdfEl.classList.add("bg-blue-700");
        }
        // --- Hose Reel ---
        hoseEl.classList.remove("bg-red-500", "bg-blue-700");

        // Get hose reel animation elements
        const hoseContainer = document.getElementById("hoseReelContainer");
        const hoseImage1 = document.getElementById("hoseReelImage1");
        const hoseImage2 = document.getElementById("hoseReelImage2");
        const directionIcon = document.getElementById("directionIcon");
        const directionArrow = document.getElementById("directionArrow");
        const hoseLabel = document.getElementById("hoseReelLabel");

        if (data.rewardHoseReel === 1 || data.rewardHoseReel === true) {
            hoseEl.textContent = "Reward";
            hoseEl.classList.add("bg-red-500");

            // Show animation - Reward
            if (hoseContainer) hoseContainer.classList.remove("hidden");
            if (directionIcon) directionIcon.classList.remove("hidden");
            if (hoseLabel) hoseLabel.classList.remove("hidden");
            if (directionArrow) directionArrow.textContent = "↺";

            // Start image alternation (no rotation)
            startImageAlternation();

        } else if (data.fordwardHoseReel === 1 || data.fordwardHoseReel === true) {
            hoseEl.textContent = "Forward";
            hoseEl.classList.add("bg-red-500");

            // Show animation - Forward
            if (hoseContainer) hoseContainer.classList.remove("hidden");
            if (directionIcon) directionIcon.classList.remove("hidden");
            if (hoseLabel) hoseLabel.classList.remove("hidden");
            if (directionArrow) directionArrow.textContent = "↻";

            // Start image alternation (no rotation)
            startImageAlternation();

        } else {
            hoseEl.textContent = "OFF";
            hoseEl.classList.add("bg-blue-700");

            // Hide animation
            if (hoseContainer) hoseContainer.classList.add("hidden");
            if (directionIcon) directionIcon.classList.add("hidden");
            if (hoseLabel) hoseLabel.classList.add("hidden");

            // Stop image alternation
            stopImageAlternation();
        }
    }

    // Hose reel image alternation
    let imageAlternationInterval;

    function startImageAlternation() {
        stopImageAlternation(); // Clear any existing interval

        const hoseImage1 = document.getElementById("hoseReelImage1");
        const hoseImage2 = document.getElementById("hoseReelImage2");

        if (!hoseImage1 || !hoseImage2) return;

        let showFirst = true;
        imageAlternationInterval = setInterval(() => {
            if (showFirst) {
                hoseImage1.style.opacity = "1";
                hoseImage2.style.opacity = "0";
            } else {
                hoseImage1.style.opacity = "0";
                hoseImage2.style.opacity = "1";
            }
            showFirst = !showFirst;
        }, 100); // Switch every 300ms for faster animation
    }

    function stopImageAlternation() {
        if (imageAlternationInterval) {
            clearInterval(imageAlternationInterval);
            imageAlternationInterval = null;
        }

        // Reset to default state
        const hoseImage1 = document.getElementById("hoseReelImage1");
        const hoseImage2 = document.getElementById("hoseReelImage2");

        if (hoseImage1) hoseImage1.style.opacity = "1";
        if (hoseImage2) hoseImage2.style.opacity = "0";
    }

    // input
    const inputSignal = ["overrideButton", "cabinetDoor", "parkingBrakeSignal", "platformDoor", "joystickUp", "joystickDown", "limitUp", "limitDown", "connerSenor", "interlockNozzle", "overfillSensor", "groundInterlock", "waterInFilter", "dpSensor", "emergencyButton", "deadmanButton"];

    function updateInputSectionByData(data) {
        const inputSection = document.getElementById("inputSection");
        if (!inputSection) return;
        const indicators = inputSection.querySelectorAll(".rounded-full");
        indicators.forEach((el, index) => {
            const signalKey = inputSignal[index];
            if (!signalKey) return;
            const value = data[signalKey];
            // Xóa màu cũ
            el.classList.remove("bg-red-500", "bg-blue-700");
            // Gán màu mới
            if (value === true || value === 1 || value === "true") {
                el.classList.add("bg-red-500");
            } else {
                el.classList.add("bg-blue-700");
            }
        });
    }

    // output
    const outputSignal = ["parkingBrakeValve", "interlockLight", "overrideLight", "overfillBuzzer", "hoseReelWind", "hoseReelReWind", "deadmanLight", "ptoPumpLight", "dpLight", "waterLight"];

    function updateOutputSectionByData(data) {
        const outputSection = document.getElementById("outputSection");
        if (!outputSection) return;
        const indicators = outputSection.querySelectorAll(".rounded-full");
        indicators.forEach((el, index) => {
            const signalKey = outputSignal[index];
            if (!signalKey) return;
            const value = data[signalKey];
            // Xóa màu cũ
            el.classList.remove("bg-red-500", "bg-blue-700");
            // Gán màu mới
            if (value === true || value === 1 || value === "true") {
                el.classList.add("bg-red-500");
            } else {
                el.classList.add("bg-blue-700");
            }
        });
    }

    // alarm
    const alarmSignal = ["dpLight", "waterLight", "truckShutdown", "overrideLight"];
    const alarmDescriptions = {
        dpLight: "High difference in pressure",
        waterLight: "Water in filter",
        truckShutdown: "Emergency button pressed",
        overrideLight: "Override button pressed"
    };
    const alarmHistory = [];
    const lastAlarmStates = {};
    // Hàm định dạng thời gian theo dạng dd/MM/yyyy hh:mm SA/CH
    function getVietnamFormattedTime() {
        const now = new Date();
        const day = String(now.getDate()).padStart(2, "0");
        const month = String(now.getMonth() + 1).padStart(2, "0");
        const year = now.getFullYear();
        let hour = now.getHours();
        const minute = String(now.getMinutes()).padStart(2, "0");
        const isPM = hour >= 12;
        const period = isPM ? "CH" : "SA";
        hour = hour % 12;
        if (hour === 0) hour = 12;
        return `${day}/${month}/${year} ${String(hour).padStart(2, "0")}:${minute} ${period}`;
    }
    // Hàm parse lại thời gian Việt Nam về đối tượng Date để so sánh
    function parseVietnamDatetime(datetimeStr) {
        // Ví dụ: "01/08/2025 01:34 CH"
        const [datePart, timePart, period] = datetimeStr.split(" ");
        const [day, month, year] = datePart.split("/").map(Number);
        let [hour, minute] = timePart.split(":").map(Number);
        if (period === "CH" && hour < 12) hour += 12;
        if (period === "SA" && hour === 12) hour = 0;
        return new Date(year, month - 1, day, hour, minute);
    }
    // Xử lý dữ liệu nhận được từ WebSocket
    function handleAlarmData(data) {
        const timestamp = getVietnamFormattedTime();
        alarmSignal.forEach(signal => {
            const currentValue = (data[signal] === true || data[signal] === 1 || data[signal] === "true");
            const lastValue = lastAlarmStates[signal];
            if (lastValue === undefined || currentValue !== lastValue) {
                lastAlarmStates[signal] = currentValue;
                const entry = {
                    time: timestamp,
                    name: signal,
                    value: currentValue ? "ON" : "OFF",
                    description: alarmDescriptions[signal] || ""
                };
                alarmHistory.push(entry);
                renderAlarmTable();
            }
        });
    }
    // Hiển thị lại toàn bộ bảng (sau khi có bản ghi mới hoặc người dùng lọc)
    function renderAlarmTable() {
        const tableBody = document.getElementById("alarmTableBody");
        tableBody.innerHTML = "";
        const fromTimeStr = document.getElementById("fromTime").value;
        const toTimeStr = document.getElementById("toTime").value;
        const nameFilter = document.getElementById("nameFilter").value.toLowerCase();
        const fromTime = fromTimeStr ? new Date(fromTimeStr) : null;
        const toTime = toTimeStr ? new Date(toTimeStr) : null;
        // Lọc danh sách
        const filtered = alarmHistory.filter(entry => {
            const entryTime = parseVietnamDatetime(entry.time);
            let show = true;
            if (fromTime && entryTime < fromTime) show = false;
            if (toTime && entryTime > toTime) show = false;
            if (nameFilter && !entry.name.toLowerCase().includes(nameFilter)) show = false;
            return show;
        });
        // Chỉ lấy 7 bản ghi gần nhất (cuối mảng)
        const latestEntries = filtered.slice(-7);
        latestEntries.forEach(entry => {
            const row = document.createElement("tr");
            row.className = entry.value === "ON" ? "bg-red-200 text-red-800" : "bg-blue-200 text-blue-800";
            row.innerHTML = `
                <td>${entry.time}</td>
                <td>${entry.name}</td>
                <td>${entry.value}</td>
                <td>${entry.description}</td>
            `;
            tableBody.appendChild(row);
        });
    }
    // Khi người dùng chọn thời gian
    function filterAlarmByTime() {
        renderAlarmTable();
    }
    // Khi người dùng gõ tên cảnh báo
    function filterAlarmTable() {
        renderAlarmTable();
    }
    // Tải PDF
    function downloadPDF() {
        const {
            jsPDF
        } = window.jspdf;
        const doc = new jsPDF();
        const now = new Date();
        const currentDate = now.toLocaleDateString("vi-VN");
        const currentTime = now.toLocaleTimeString("vi-VN");
        const title = "Alarm History Table - Project <?= $project_name ?>";
        const datetime = `Date: ${currentDate} ${currentTime}`;
        doc.setFontSize(16);
        doc.text(title, 105, 15, {
            align: "center"
        });
        doc.setFontSize(10);
        doc.text(datetime, 105, 22, {
            align: "center"
        });
        const table = document.getElementById("alarmTable");
        const rows = [];
        const tbody = document.getElementById("alarmTableBody");
        for (const row of tbody.rows) {
            const rowData = [];
            for (const cell of row.cells) {
                rowData.push(cell.innerText);
            }
            rows.push(rowData);
        }
        doc.autoTable({
            head: [
                ["Timestamp", "Name", "Value", "Description"]
            ],
            body: rows,
            startY: 28,
            theme: 'striped',
            styles: {
                fontSize: 9
            },
            headStyles: {
                fillColor: [0, 128, 0]
            }
        });
        doc.save("alarm-history-project-<?= $project_name ?>.pdf");
    }

    // maintenance

    const maintenanceSignal = ["timeStartJet", "timeCheckJet", "timeReplaceJet", "timeStartTank", "timeCheckTank", "timeCleanTank"];
    const maintenanceSignal1 = ["remainingCheckJet", "remainingReplacedJet", "remainingCheckTank", "remainingCleanedTank"];

    function updateRemainingData(data) {
        // Cập nhật remaining1-4 với giá trị từ maintenanceSignal1
        maintenanceSignal1.forEach((signal, index) => {
            const elementId = `remaining${index + 1}`;
            const element = document.getElementById(elementId);
            if (element && data[signal] !== undefined) {
                element.textContent = `Remaining: ${data[signal]}`;
            }
        });
    }

    function updateMaintenanceData(data) {
        // Cập nhật currentDate1-6 với giá trị từ maintenanceSignal
        maintenanceSignal.forEach((signal, index) => {
            const elementId = `currentDate${index + 1}`;
            const element = document.getElementById(elementId);
            if (element && data[signal] !== undefined) {
                let dateValue = data[signal];

                try {
                    // Xử lý các format khác nhau
                    if (typeof dateValue === 'string') {
                        // Kiểm tra nếu là ISO string (2026-02-02T09:40:14.000Z)
                        if (dateValue.includes('T') && (dateValue.includes('Z') || dateValue.includes('+'))) {
                            const date = new Date(dateValue);
                            dateValue = date.toLocaleDateString('vi-VN');
                        }
                        // Nếu có format "DD/MM/YYYY HH:mm:ss" thì chỉ lấy "DD/MM/YYYY"
                        else if (dateValue.includes(' ')) {
                            dateValue = dateValue.split(' ')[0];
                        }
                        // Nếu đã là format DD/MM/YYYY thì giữ nguyên
                    } else if (dateValue instanceof Date) {
                        // Nếu là Date object thì format thành DD/MM/YYYY
                        dateValue = dateValue.toLocaleDateString('vi-VN');
                    }

                    element.textContent = dateValue;
                } catch (error) {
                    console.error(`Error parsing date for ${signal}:`, error);
                    element.textContent = "---";
                }
            }
        });

        // Giữ nguyên currentDate7-10 với ngày hiện tại
        const now = new Date();
        const formatted = now.toLocaleDateString('vi-VN');
        document.getElementById("currentDate7").textContent = `${formatted}`;
        document.getElementById("currentDate8").textContent = `${formatted}`;
        document.getElementById("currentDate9").textContent = `${formatted}`;
        document.getElementById("currentDate10").textContent = `${formatted}`;
    }

    // Khởi tạo với giá trị mặc định
    function initMaintenanceData() {
        // currentDate1-6 hiển thị "---" ban đầu
        for (let i = 1; i <= 6; i++) {
            const element = document.getElementById(`currentDate${i}`);
            if (element) element.textContent = "---";
        }

        // remaining1-4 hiển thị "Remaining: ---" ban đầu
        for (let i = 1; i <= 4; i++) {
            const element = document.getElementById(`remaining${i}`);
            if (element) element.textContent = "Remaining: ---";
        }

        // currentDate7-10 hiển thị ngày hiện tại
        const now = new Date();
        const formatted = now.toLocaleDateString('vi-VN');
        document.getElementById("currentDate7").textContent = `${formatted}`;
        document.getElementById("currentDate8").textContent = `${formatted}`;
        document.getElementById("currentDate9").textContent = `${formatted}`;
        document.getElementById("currentDate10").textContent = `${formatted}`;
    }

    initMaintenanceData();


    // Test data để kiểm tra giao diện (sẽ bị ghi đè bởi WebSocket data)

    // Load test data after 2 seconds if no WebSocket data received
    let hasReceivedData = false;
    setTimeout(() => {
        if (!hasReceivedData) {
            console.log("⚠️ No WebSocket data received, loading test data...");
        }
    }, 2000);

    // WebSocket connection for Project <?= $project_name ?>
    console.log("🔌 Attempting to connect to WebSocket...");
    const ws = new WebSocket("ws://localhost:3001");

    ws.onmessage = (event) => {
        try {
            hasReceivedData = true; // Đánh dấu đã nhận được data
            const data = JSON.parse(event.data);

            // monitor
            const pipePressureEl = document.getElementById("pipePressure");
            const tankLevelEl = document.getElementById("tankLevel");
            const fuelEl = document.getElementById("gauge-fuel");

            if (pipePressureEl && data.pipePressure !== undefined) {
                pipePressureEl.textContent = `${parseFloat(data.pipePressure).toFixed(2)} bar`;
            }
            if (tankLevelEl && data.tankLevel !== undefined) {
                tankLevelEl.textContent = `${parseFloat(data.tankLevel).toFixed(0)} Lits`;
            }
            if (fuelEl && data.fuelDelivered !== undefined) {
                fuelEl.textContent = `${parseFloat(data.fuelDelivered).toFixed(0)} Lits`;
            }
            // confirm delivery steps
            updateStepColorsByData(data);
            // stop delivery signal
            updateStopSignalByData(data);
            // cotrol panel
            updateControlPanelByData(data);
            // input
            updateInputSectionByData(data);
            // output
            updateOutputSectionByData(data);
            // alarm
            handleAlarmData(data);
            // maintenance data
            updateMaintenanceData(data);
            // remaining data
            updateRemainingData(data);
        } catch (e) {
            console.error("Lỗi parse JSON:", e, event.data);
        }
    };
</script>