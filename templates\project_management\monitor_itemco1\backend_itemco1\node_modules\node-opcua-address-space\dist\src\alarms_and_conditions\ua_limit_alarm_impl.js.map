{"version": 3, "file": "ua_limit_alarm_impl.js", "sourceRoot": "", "sources": ["../../../src/alarms_and_conditions/ua_limit_alarm_impl.ts"], "names": [], "mappings": ";;;AAKA,yDAA2C;AAC3C,iEAAkD;AAClD,iEAAkD;AAClD,uDAAmD;AAEnD,mEAAiE;AACjE,2DAA8D;AAK9D,uEAAiE;AAEjE,MAAM,UAAU,GAAG,IAAA,kCAAe,EAAC,qBAAqB,CAAC,CAAC;AAO1D,MAAM,kCAAkC,GAAe;IACnD,6BAAQ,CAAC,MAAM;IACf,6BAAQ,CAAC,KAAK;IACd,6BAAQ,CAAC,IAAI;IACb,6BAAQ,CAAC,KAAK;IACd,6BAAQ,CAAC,KAAK;IACd,6BAAQ,CAAC,KAAK;IACd,6BAAQ,CAAC,MAAM;IACf,6BAAQ,CAAC,MAAM;CAClB,CAAC;AAKF,MAAa,gBAAiB,SAAQ,8CAAoB;IACtD;OACG;IACI,MAAM,CAAC,WAAW,CACrB,SAA2B,EAC3B,gBAA+C,EAC/C,OAAqC,EACrC,IAAqC;QAErC,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;QAE5C,2BAA2B;QAC3B,wGAAwG;QACxG,IAAA,0BAAM,EACF,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAC1D,8DAA8D,CACjE,CAAC;QAEF,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAC5C,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE,CAAC;YACjE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxC,KAAK,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,CAAC;YAC7D,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpC,KAAK,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC;YAC5D,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACnC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACnC,KAAK,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE,CAAC;YAC/D,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtC,KAAK,EAAE,CAAC;QACZ,CAAC;QAED,4DAA4D;QAC5D,MAAM,SAAS,GAAG,8CAAoB,CAAC,WAAW,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAqB,CAAC;QACnH,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAE7D,IAAA,0BAAM,EAAC,SAAS,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,CAAC;QAE7C,MAAM,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAe,CAAC;QAC5E,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACpD,CAAC;QACD,IAAA,0BAAM,EAAC,SAAS,CAAC,SAAS,KAAK,iCAAS,CAAC,QAAQ,CAAC,CAAC;QAEnD,wDAAwD;QACxD,kBAAkB;QAClB,+FAA+F;QAC/F,iGAAiG;QACjG,+FAA+F;QAC/F,qGAAqG;QACrG,iGAAiG;QACjG,gGAAgG;QAChG,6FAA6F;QAC7F,8BAA8B;QAC9B,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,QAAQ,GAAG,YAAY,CAAC,8BAA8B,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAEzF,IAAI,CAAC,CAAC,KAAK,kCAAkC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,MAAM,OAAO,GAAG,uDAAuD,kCAAkC;iBACpG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,6BAAQ,CAAC,CAAC,CAAC,CAAC;iBACvB,IAAI,CAAC,GAAG,CAAC,SAAS,6BAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5C,UAAU,CAAC,OAAO,CAAC,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACxG,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAc,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,CAAC;YAC7D,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC;YAC5D,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,IAAI,OAAO,CAAC,WAAW,IAAI,SAAS,EAAE,CAAC;YACnG,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;QAED;;;;;WAKG;QACH,IAAA,0BAAM,EAAC,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,iCAAS,CAAC,QAAQ,CAAC,CAAC;QAC7D,SAAS,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAExF,0CAA0C;QAC1C,SAAS,CAAC,0BAA0B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACxD,SAAS,CAAC,YAAY,EAAE,CAAC;QAEzB,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;OACG;IACI,gBAAgB;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACzD,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;IACtD,CAAC;IAED;OACG;IACI,YAAY;QACf,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;IAClD,CAAC;IAED;OACG;IACI,WAAW;QACd,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;IACjD,CAAC;IAED;OACG;IACI,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;IACpD,CAAC;IAED;OACG;IACI,gBAAgB,CAAC,KAAa;QACjC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QAC3F,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,6BAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;OACG;IACI,YAAY,CAAC,KAAa;QAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACvF,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,6BAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;IAC5E,CAAC;IAED;OACG;IACI,WAAW,CAAC,KAAa;QAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACtF,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,6BAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;OACG;IACI,cAAc,CAAC,KAAa;QAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;QACzF,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,6BAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;IAC9E,CAAC;IAES,uBAAuB,CAAC,SAAoB;QAClD,IAAA,0BAAM,EAAC,SAAS,YAAY,iCAAS,CAAC,CAAC;QAEvC,IACI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,oCAAW,CAAC,wBAAwB,CAAC;YACjE,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,oCAAW,CAAC,qBAAqB,CAAC,EAChE,CAAC;YACC,mDAAmD;YACnD,OAAO;QACX,CAAC;QACD,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;YACnC,qBAAqB;YACrB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC/B,OAAO;QACX,CAAC;QACD,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,KAAK,6BAAQ,CAAC,IAAI,EAAE,CAAC;YAC7C,qBAAqB;YACrB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC/B,OAAO;QACX,CAAC;QACD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;QACpC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAES,0BAA0B,CAAC,KAAa;QAC9C,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACpE,CAAC;IAES,YAAY;QAClB,0EAA0E;QAC1E,+BAA+B;QAC/B,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAES,8BAA8B;QACpC,IAAA,0BAAM,EAAC,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,CAAC;QACxC,6BAA6B;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtC,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACpC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;CACJ;AA3OD,4CA2OC"}