{"version": 3, "file": "ua_exclusive_limit_alarm_impl.js", "sourceRoot": "", "sources": ["../../../src/alarms_and_conditions/ua_exclusive_limit_alarm_impl.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,yDAA2C;AAM3C,gFAA8E;AAG9E,+DAA0D;AAE1D,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAM/D,MAAa,yBAA0B,SAAQ,sCAAgB;IAC3D;;;;;;;OAOG;IACI,MAAM,CAAC,WAAW,CACrB,SAA2B,EAC3B,IAAmC,EACnC,OAAqC,EACrC,IAAqC;QAErC,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;QAE5C,MAAM,kBAAkB,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE5D,0BAA0B;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,wCAAwC,GAAG,IAAI,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,uBAAuB,GAAG,YAAY,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACtF,0BAA0B;QAC1B,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,IAAI,GAAG,sCAAgB,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1E,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,yBAAyB,CAAC,SAAS,CAAC,CAAC;QACjE,MAAM,KAAK,GAAG,IAAwC,CAAC;QACvD,IAAA,0BAAM,EAAC,KAAK,YAAY,yBAAyB,CAAC,CAAC;QACnD,IAAA,0BAAM,EAAC,KAAK,YAAY,sCAAgB,CAAC,CAAC;QAE1C,mDAAmD;QACnD,IAAA,0BAAM,EAAC,KAAK,CAAC,UAAU,EAAE,yBAAyB,CAAC,CAAC;QACpD,IAAA,4CAAqB,EAAC,KAAK,CAAC,UAAiC,CAAC,CAAC;QAE/D,8BAA8B;QAC9B,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAElC,KAAK,CAAC,WAAW,EAAE,CAAC;QAEpB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,mBAAmB,CAAC,SAAwB,EAAE,QAAiB,EAAE,KAAa;QACjF,IAAA,0BAAM,EAAC,SAAS,KAAK,IAAI,IAAI,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC;QAC5D,IAAA,0BAAM,EAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,4BAA4B,GAAG,SAAS,CAAC,CAAC;QAErF,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAE9C,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACJ,IAAA,0BAAM,EAAC,SAAS,KAAK,IAAI,CAAC,CAAC;YAC3B,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;QACD,KAAK,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAEM,0BAA0B,CAAC,KAAa;QAC3C,IAAA,0BAAM,EAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QACxB,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,IAAI,KAAK,GAAG,IAAI,CAAC;QAEjB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;QAEnD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,EAAE,GAAG,KAAK,EAAE,CAAC;YACxD,KAAK,GAAG,UAAU,CAAC;YACnB,QAAQ,GAAG,IAAI,CAAC;QACpB,CAAC;aAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE,GAAG,KAAK,EAAE,CAAC;YACvD,KAAK,GAAG,MAAM,CAAC;YACf,QAAQ,GAAG,IAAI,CAAC;QACpB,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,EAAE,GAAG,KAAK,EAAE,CAAC;YAC3D,KAAK,GAAG,QAAQ,CAAC;YACjB,QAAQ,GAAG,IAAI,CAAC;QACpB,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE,GAAG,KAAK,EAAE,CAAC;YACrD,KAAK,GAAG,KAAK,CAAC;YACd,QAAQ,GAAG,IAAI,CAAC;QACpB,CAAC;QAED,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;YACrB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;CACJ;AA1FD,8DA0FC"}