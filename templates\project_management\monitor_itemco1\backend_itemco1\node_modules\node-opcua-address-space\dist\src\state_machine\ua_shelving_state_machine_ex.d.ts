/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
import { DataType } from "node-opcua-variant";
import { UAProperty, UAMethod, UAObject } from "node-opcua-address-space-base";
import { UAState } from "node-opcua-nodeset-ua";
import { UAShelvedStateMachineEx } from "../../source/interfaces/state_machine/ua_shelved_state_machine_ex";
import { UATransitionEx } from "../../source/interfaces/state_machine/ua_transition_ex";
import { UAStateMachineImpl } from "./finite_state_machine";
export interface UAShelvedStateMachineHelper {
    _timer: NodeJS.Timeout | null;
    _shelvedTime: Date;
    _unshelvedTime: Date;
    _duration: number;
}
export interface UAShelvedStateMachineExImpl extends UAShelvedStateMachineHelper {
    unshelveTime: UAProperty<number, /*z*/ DataType.Double>;
    unshelved: UAState;
    timedShelved: UAState;
    oneShotShelved: UAState;
    unshelvedToTimedShelved: UATransitionEx;
    unshelvedToOneShotShelved: UATransitionEx;
    timedShelvedToUnshelved: UATransitionEx;
    timedShelvedToOneShotShelved: UATransitionEx;
    oneShotShelvedToUnshelved: UATransitionEx;
    oneShotShelvedToTimedShelved: UATransitionEx;
    timedShelve: UAMethod;
    timedShelve2?: UAMethod;
    unshelve: UAMethod;
    unshelve2?: UAMethod;
    oneShotShelve: UAMethod;
    oneShotShelve2?: UAMethod;
}
export declare class UAShelvedStateMachineExImpl extends UAStateMachineImpl implements UAShelvedStateMachineEx {
    static promote(object: UAObject): UAShelvedStateMachineEx;
}
export declare function _clear_timer_if_any(shelvingState: UAShelvedStateMachineExImpl): void;
