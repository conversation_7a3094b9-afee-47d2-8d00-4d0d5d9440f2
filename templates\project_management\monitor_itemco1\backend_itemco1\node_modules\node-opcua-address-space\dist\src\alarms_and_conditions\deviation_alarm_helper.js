"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeviationAlarmHelper_getSetpointNodeNode = DeviationAlarmHelper_getSetpointNodeNode;
exports.DeviationAlarmHelper_getSetpointValue = DeviationAlarmHelper_getSetpointValue;
exports.DeviationAlarmHelper_onSetpointDataValueChange = DeviationAlarmHelper_onSetpointDataValueChange;
exports.DeviationAlarmHelper_install_setpoint = DeviationAlarmHelper_install_setpoint;
/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_variant_1 = require("node-opcua-variant");
const node_opcua_nodeid_1 = require("node-opcua-nodeid");
function DeviationAlarmHelper_getSetpointNodeNode() {
    (0, node_opcua_assert_1.assert)(this.setpointNode.readValue().value.dataType === node_opcua_variant_1.DataType.NodeId);
    const nodeId = this.setpointNode.readValue().value.value;
    const node = this.addressSpace.findNode(nodeId);
    (0, node_opcua_assert_1.assert)(node === this.setpointNodeNode);
    if (!node) {
        return undefined;
    }
    return this.setpointNodeNode;
}
function DeviationAlarmHelper_getSetpointValue() {
    (0, node_opcua_assert_1.assert)(Object.prototype.hasOwnProperty.call(this, "setpointNode"));
    (0, node_opcua_assert_1.assert)(Object.prototype.hasOwnProperty.call(this, "setpointNodeNode"));
    if (!this.setpointNodeNode) {
        return null;
    }
    const setpointDataValue = this.setpointNodeNode.readValue();
    if (setpointDataValue.statusCode.isNotGood()) {
        return null;
    }
    const node = this.getSetpointNodeNode();
    if (!node) {
        return null;
    }
    return node.readValue().value.value;
}
function DeviationAlarmHelper_onSetpointDataValueChange(dataValue) {
    this._setStateBasedOnInputValue(this.getInputNodeValue());
}
function DeviationAlarmHelper_install_setpoint(options) {
    (0, node_opcua_assert_1.assert)(this.setpointNode.browseName.toString() === "SetpointNode");
    const addressSpace = this.addressSpace;
    if (options.setpointNode) {
        const setpointNodeNode = addressSpace._coerceNode(options.setpointNode);
        (0, node_opcua_assert_1.assert)(setpointNodeNode, "Expecting a valid setpoint node");
        this.setpointNodeNode = addressSpace._coerceNode(options.setpointNode);
        this.setpointNode.setValueFromSource({ dataType: "NodeId", value: this.setpointNodeNode.nodeId });
        // install inputNode monitoring for change
        this.setpointNodeNode.on("value_changed", (newDataValue) => {
            this._onSetpointDataValueChange(newDataValue);
        });
    }
    else {
        this.setpointNodeNode = undefined;
        this.setpointNode.setValueFromSource({ dataType: "NodeId", value: node_opcua_nodeid_1.NodeId.nullNodeId });
    }
}
//# sourceMappingURL=deviation_alarm_helper.js.map