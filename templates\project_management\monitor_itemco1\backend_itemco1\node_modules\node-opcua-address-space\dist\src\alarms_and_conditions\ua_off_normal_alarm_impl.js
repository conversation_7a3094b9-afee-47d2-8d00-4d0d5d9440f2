"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UAOffNormalAlarmImpl = void 0;
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_nodeid_1 = require("node-opcua-nodeid");
const node_opcua_utils_1 = require("node-opcua-utils");
const node_opcua_variant_1 = require("node-opcua-variant");
const ua_discrete_alarm_impl_1 = require("./ua_discrete_alarm_impl");
function isEqual(value1, value2) {
    return value1 === value2;
}
/**
 * The OffNormalAlarmType is a specialization of the DiscreteAlarmType intended to represent a
 * discrete Condition that is considered to be not normal.
 * This sub type is usually used to indicate that a discrete value is in an Alarm state, it is active as
 * long as a non-normal value is present.
 */
class UAOffNormalAlarmImpl extends ua_discrete_alarm_impl_1.UADiscreteAlarmImpl {
    /**
     * When the value of inputNode doesn't match the normalState node value, then the alarm is raised.
     *
     */
    static instantiate(namespace, limitAlarmTypeId, options, data) {
        const addressSpace = namespace.addressSpace;
        const offNormalAlarmType = addressSpace.findEventType("OffNormalAlarmType");
        /* istanbul ignore next */
        if (!offNormalAlarmType) {
            throw new Error("cannot find offNormalAlarmType");
        }
        (0, node_opcua_assert_1.assert)(Object.prototype.hasOwnProperty.call(options, "inputNode"), "must provide inputNode"); // must provide a inputNode
        (0, node_opcua_assert_1.assert)(Object.prototype.hasOwnProperty.call(options, "normalState"), "must provide a normalState Node"); // must provide a inputNode
        options.optionals = options.optionals || [];
        (0, node_opcua_assert_1.assert)(Object.prototype.hasOwnProperty.call(options, "inputNode"), "must provide inputNode"); // must provide a inputNode
        const alarmNode = ua_discrete_alarm_impl_1.UADiscreteAlarmImpl.instantiate(namespace, limitAlarmTypeId, options, data);
        Object.setPrototypeOf(alarmNode, UAOffNormalAlarmImpl.prototype);
        /**
         * The InputNode Property provides the NodeId of the Variable the Value of which is used as primary input in
         * the calculation of the Alarm state.
         *
         * If this Variable is not in the AddressSpace, a NULL NodeId shall be provided.
         *
         * In some systems, an Alarm may be calculated based on multiple Variables Values;
         * it is up to the system to determine which Variable’s NodeId is used.
         */
        const inputNode = addressSpace._coerceNode(options.inputNode);
        // note: alarmNode.inputNode.readValue() already set by DiscreteAlarmImpl.instantiate
        if (inputNode) {
            // install inputNode Node monitoring for change
            alarmNode.installInputNodeMonitoring(options.inputNode);
        }
        /**
         * The NormalState Property is a Property that points to a Variable which has a value that corresponds to one
         * of the possible values of the Variable pointed to by the InputNode Property where the NormalState Property
         * Variable value is the value that is considered to be the normal state of the Variable pointed to by the InputNode
         * Property. When the value of the Variable referenced by the InputNode Property is not equal to the value of the
         * NormalState Property the Alarm is Active.
         *
         * If this Variable is not in the AddressSpace, a NULL NodeId shall be provided.
         *
         */
        if (options.normalState) {
            const normalState = addressSpace._coerceNode(options.normalState);
            const normalStateNodeId = normalState ? normalState.nodeId : new node_opcua_nodeid_1.NodeId();
            alarmNode.normalState.setValueFromSource({ dataType: node_opcua_variant_1.DataType.NodeId, value: normalStateNodeId });
            alarmNode.normalState.on("value_changed", (newDataValue /*, oldDataValue: DataValue*/) => {
                // The node that contains the normalState value has changed.
                //   we must remove the listener on current normalState and replace
                //   normalState with the new one and set listener again
                //   to do:
            });
            if (normalState) {
                // install normalState monitoring for change
                normalState.on("value_changed", (newDataValue /*, oldDataValue: DataValue*/) => {
                    alarmNode._onNormalStateDataValueChange(newDataValue);
                });
            }
        }
        else {
            alarmNode.normalState.setValueFromSource({ dataType: node_opcua_variant_1.DataType.NodeId, value: node_opcua_nodeid_1.NodeId.nullNodeId });
        }
        alarmNode._mayBe_updateAlarmState();
        return alarmNode;
    }
    // HasProperty Variable NormalState NodeId PropertyType Mandatory
    // The NormalState Property is a Property that points to a Variable which has a value that
    // corresponds to one of the possible values of the Variable pointed to by the InputNode
    // Property where the NormalState Property Variable value is the value that is considered to be
    // the normal state of the Variable pointed to by the InputNode Property. When the value of the
    // Variable referenced by the InputNode Property is not equal to the value of the NormalState
    // Property the Alarm is Active. If this Variable is not in the AddressSpace, a Null NodeId shall
    // be provided.
    getNormalStateNode() {
        const nodeId = this.normalState.readValue().value.value;
        const node = this.addressSpace.findNode(nodeId);
        if (!node) {
            return null;
        }
        return node;
    }
    /**
     */
    getNormalStateValue() {
        const normalStateNode = this.getNormalStateNode();
        if (!normalStateNode) {
            return null;
        }
        return normalStateNode.readValue().value.value;
    }
    /**
     */
    setNormalStateValue(value) {
        const normalStateNode = this.getNormalStateNode();
        throw new Error("Not Implemented yet");
    }
    updateAlarmState(isActive, message) {
        if (isActive === this.activeState.getValue()) {
            // no change => ignore !
            return;
        }
        const stateName = isActive ? "Active" : "Inactive";
        // also raise the event
        this._signalNewCondition(stateName, isActive, message);
        if (!isActive) {
            this.currentBranch().setRetain(false);
        }
    }
    _mayBe_updateAlarmState(normalStateValue, inputValue) {
        if ((0, node_opcua_utils_1.isNullOrUndefined)(normalStateValue) || (0, node_opcua_utils_1.isNullOrUndefined)(inputValue)) {
            this.activeState.setValue(false);
            return;
        }
        const isActive = !isEqual(normalStateValue, inputValue);
        this.updateAlarmState(isActive, "automatique update");
    }
    _onInputDataValueChange(dataValue) {
        if (dataValue.statusCode.isNotGood()) {
            // what shall we do ?
            return;
        }
        if (dataValue.value.dataType === node_opcua_variant_1.DataType.Null) {
            // what shall we do ?
            return;
        }
        const inputValue = dataValue.value.value;
        const normalStateValue = this.getNormalStateValue();
        this._mayBe_updateAlarmState(normalStateValue, inputValue);
    }
    _onNormalStateDataValueChange(dataValue) {
        if (dataValue.statusCode.isNotGood()) {
            // what shall we do ?
            return;
        }
        if (dataValue.value.dataType === node_opcua_variant_1.DataType.Null) {
            // what shall we do ?
            return;
        }
        const normalStateValue = dataValue.value.value;
        const inputValue = this.getInputNodeValue();
        this._mayBe_updateAlarmState(normalStateValue, inputValue);
    }
}
exports.UAOffNormalAlarmImpl = UAOffNormalAlarmImpl;
//# sourceMappingURL=ua_off_normal_alarm_impl.js.map