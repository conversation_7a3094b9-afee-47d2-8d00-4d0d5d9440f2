import { UAState, UAStateVariable, UATransition, UATransitionVariable } from "node-opcua-nodeset-ua";
import { LocalizedText } from "node-opcua-data-model";
import { NodeId } from "node-opcua-nodeid";
import { BaseNode, UAObject, UAObjectType } from "node-opcua-address-space-base";
import { UAStateMachineEx, TransitionSelector, UAStateMachineTypeHelper } from "../../source/interfaces/state_machine/ua_state_machine_type";
import { UAObjectImpl } from "../ua_object_impl";
import { UATransitionEx } from "../../source/interfaces/state_machine/ua_transition_ex";
import { UAObjectTypeImpl } from "../ua_object_type_impl";
export declare interface UATransitionImpl extends UATransition, UATransitionEx {
}
export declare class UATransitionImpl implements UATransition, UATransitionEx {
}
export interface UAStateMachineImpl {
    currentState: UAStateVariable<LocalizedText>;
    lastTransition?: UATransitionVariable<LocalizedText>;
    _currentStateNode: UAState | null;
}
export declare function getFiniteStateMachineTypeStates(uaFiniteStateMachineType: UAObjectType): UAState[];
export declare function getFiniteStateMachineTypeTransitions(uaFiniteStateMachineType: UAObjectType): UATransitionEx[];
export declare function getFiniteStateMachineTypeStateByName(uaFiniteStateMachineType: UAObjectType, stateName: string): UAState | null;
export declare class UAStateMachineImpl extends UAObjectImpl implements UAStateMachineEx {
    getStates(): UAState[];
    get states(): UAState[];
    /**
     * @param name  the name of the state to get
     * @return the state with the given name
     */
    getStateByName(name: string): UAState | null;
    getTransitions(): UATransitionEx[];
    get transitions(): UATransitionEx[];
    /**
     * return the node InitialStateType
     * @property initialState
     */
    get initialState(): UAState | null;
    /**
     *
     * @param node
     * @private
     */
    _coerceNode(node: UAState | BaseNode | null | string | NodeId): BaseNode | null;
    /**
     */
    isValidTransition(toStateNode: UAState | string, predicate?: TransitionSelector): boolean;
    /**
     */
    findTransitionNode(fromStateNode: NodeId | UAState | string | null, toStateNode: NodeId | UAState | string | null, predicate?: TransitionSelector): UATransitionEx | null;
    get currentStateNode(): UAState | null;
    /**
     * @property currentStateNode
     * @type BaseNode
     */
    set currentStateNode(value: UAState | null);
    /**
     */
    getCurrentState(): string | null;
    /**
     */
    setState(toStateNode: string | UAState | null, predicate?: TransitionSelector): void;
    /**
     * @internal
     * @private|
     */
    _post_initialize(): void;
}
export declare function promoteToStateMachine(node: UAObject): UAStateMachineEx;
export declare class UAStateMachineTypeImpl extends UAObjectTypeImpl implements UAStateMachineTypeHelper {
    getStateByName(name: string): UAState | null;
    getStates(): UAState[];
    getTransitions(): UATransitionEx[];
    _post_initialize(): void;
}
export declare function promoteToStateMachineType(node: UAObject): UAStateMachineTypeHelper;
