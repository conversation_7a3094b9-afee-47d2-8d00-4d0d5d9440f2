"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateDataTypeCorrectness = validateDataTypeCorrectness;
const chalk_1 = __importDefault(require("chalk"));
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_basic_types_1 = require("node-opcua-basic-types");
const node_opcua_debug_1 = require("node-opcua-debug");
const debugLog = (0, node_opcua_debug_1.make_debugLog)(__filename);
const doDebug = (0, node_opcua_debug_1.checkDebugFlag)(__filename);
function _dataType_toUADataType(addressSpace, dataType) {
    (0, node_opcua_assert_1.assert)(addressSpace);
    (0, node_opcua_assert_1.assert)(dataType !== node_opcua_basic_types_1.DataType.Null);
    const dataTypeNode = addressSpace.findDataType(node_opcua_basic_types_1.DataType[dataType]);
    /* istanbul ignore next */
    if (!dataTypeNode) {
        throw new Error(" Cannot find DataType " + node_opcua_basic_types_1.DataType[dataType] + " in address Space");
    }
    return dataTypeNode;
}
const validDataTypeForEnumValue = [node_opcua_basic_types_1.DataType.Int32];
// , DataType.UInt32, DataType.Int64, DataType.UInt64];
/*=
 *
 * @param addressSpace
 * @param dataTypeNodeId : the nodeId matching the dataType of the destination variable.
 * @param variantDataType: the dataType of the variant to write to the destination variable
 * @param nodeId
 * @return {boolean} true if the variant dataType is compatible with the Variable DataType
 */
function validateDataTypeCorrectness(addressSpace, dataTypeNodeId, variantDataType, allowNulls, context) {
    if (variantDataType === node_opcua_basic_types_1.DataType.Null && allowNulls) {
        return true;
    }
    if (variantDataType === node_opcua_basic_types_1.DataType.Null && !allowNulls) {
        return false;
    }
    let builtInType;
    let builtInUADataType;
    const destUADataType = addressSpace.findDataType(dataTypeNodeId);
    // istanbul ignore next
    if (!destUADataType) {
        throw new Error("Cannot find UADataType " + dataTypeNodeId.toString() + " in address Space");
    }
    if (variantDataType === node_opcua_basic_types_1.DataType.ExtensionObject) {
        const structure = addressSpace.findDataType("Structure");
        if (destUADataType.isSubtypeOf(structure)) {
            return true;
        }
        return false;
    }
    if (destUADataType.isAbstract) {
        builtInUADataType = destUADataType;
    }
    else {
        builtInType = addressSpace.findCorrespondingBasicDataType(destUADataType);
        if (builtInType === node_opcua_basic_types_1.DataType.ExtensionObject) {
            // it should have been trapped earlier
            return false;
        }
        builtInUADataType = addressSpace.findDataType(builtInType);
    }
    const enumerationUADataType = addressSpace.findDataType("Enumeration");
    // istanbul ignore next
    if (!enumerationUADataType) {
        throw new Error("cannot find Enumeration DataType node in standard address space");
    }
    if (destUADataType.isSubtypeOf(enumerationUADataType)) {
        // istanbul ignore next
        if (doDebug) {
            debugLog("destUADataType.", destUADataType.browseName.toString(), destUADataType.nodeId.toString());
            debugLog("enumerationUADataType.", enumerationUADataType.browseName.toString(), enumerationUADataType.nodeId.toString());
        }
        return validDataTypeForEnumValue.indexOf(variantDataType) >= 0;
    }
    // The value supplied for the attribute is not of the same type as the  value.
    const variantUADataType = _dataType_toUADataType(addressSpace, variantDataType);
    const dest_isSubTypeOf_variant = variantUADataType.isSubtypeOf(builtInUADataType);
    // istanbul ignore next
    if (doDebug) {
        if (dest_isSubTypeOf_variant) {
            /* istanbul ignore next*/
            debugLog(chalk_1.default.green(" ---------- Type match !!! "), " on ", context?.toString());
        }
        else {
            /* istanbul ignore next*/
            debugLog(chalk_1.default.red(" ---------- Type mismatch "), " on ", context?.toString());
        }
        debugLog(chalk_1.default.cyan(" Variable data Type is    = "), destUADataType.browseName.toString());
        debugLog(chalk_1.default.cyan(" which matches basic Type = "), builtInUADataType.browseName.toString());
        debugLog(chalk_1.default.yellow("        Actual   dataType = "), variantUADataType.browseName.toString());
    }
    return dest_isSubTypeOf_variant;
}
//# sourceMappingURL=validate_data_type_correctness.js.map