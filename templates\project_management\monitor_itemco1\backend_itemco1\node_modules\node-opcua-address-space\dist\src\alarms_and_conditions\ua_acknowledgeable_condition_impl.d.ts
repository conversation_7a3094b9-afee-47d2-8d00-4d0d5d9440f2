import { LocalizedText, LocalizedTextLike } from "node-opcua-data-model";
import { NodeId } from "node-opcua-nodeid";
import { StatusCode } from "node-opcua-status-code";
import { VariantOptions } from "node-opcua-variant";
import { INamespace, UAEventType } from "node-opcua-address-space-base";
import { AddressSpacePrivate } from "../address_space_private";
import { UAAcknowledgeableConditionEx } from "../../source/interfaces/alarms_and_conditions/ua_acknowledgeable_condition_ex";
import { ConditionSnapshot } from "../../source/interfaces/alarms_and_conditions/condition_snapshot";
import { InstantiateAlarmConditionOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_alarm_condition_options";
import { UAConditionImpl } from "./ua_condition_impl";
export declare interface UAAcknowledgeableConditionImpl extends UAAcknowledgeableConditionEx, UAConditionImpl {
    on(eventName: string, eventHandler: any): this;
    once(eventName: string, eventHandler: any): this;
}
export declare class UAAcknowledgeableConditionImpl extends UAConditionImpl implements UAAcknowledgeableConditionEx {
    /**
     */
    static instantiate(namespace: INamespace, conditionTypeId: UAEventType | NodeId | string, options: InstantiateAlarmConditionOptions, data?: Record<string, VariantOptions>): UAAcknowledgeableConditionImpl;
    static install_method_handle_on_type(addressSpace: AddressSpacePrivate): void;
    _raiseAuditConditionAcknowledgeEvent(branch: ConditionSnapshot): void;
    _raiseAuditConditionConfirmEvent(branch: ConditionSnapshot): void;
    _acknowledge_branch(conditionEventId: Buffer, comment: string | LocalizedTextLike | LocalizedText, branch: ConditionSnapshot, message: string): StatusCode;
    /**
     * @param conditionEventId The ConditionEventId field shall contain the id of the Event that was conformed
     * @private
     */
    _confirm_branch(conditionEventId: Buffer, comment: string | LocalizedTextLike, branch: ConditionSnapshot, message: string): void;
    /**
     */
    autoConfirmBranch(branch: ConditionSnapshot, comment: LocalizedTextLike): void;
    /**
     *
     */
    acknowledgeAndAutoConfirmBranch(branch: ConditionSnapshot, comment: string | LocalizedTextLike | LocalizedText): void;
}
