"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UAAcknowledgeableConditionImpl = void 0;
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_data_model_1 = require("node-opcua-data-model");
const node_opcua_status_code_1 = require("node-opcua-status-code");
const node_opcua_variant_1 = require("node-opcua-variant");
const node_opcua_debug_1 = require("node-opcua-debug");
const ua_two_state_variable_1 = require("../state_machine/ua_two_state_variable");
const condition_snapshot_impl_1 = require("./condition_snapshot_impl");
const ua_condition_impl_1 = require("./ua_condition_impl");
const debugLog = (0, node_opcua_debug_1.make_debugLog)(__filename);
const doDebug = false;
;
class UAAcknowledgeableConditionImpl extends ua_condition_impl_1.UAConditionImpl {
    /**
     */
    static instantiate(namespace, conditionTypeId, options, data) {
        const conditionNode = ua_condition_impl_1.UAConditionImpl.instantiate(namespace, conditionTypeId, options, data);
        Object.setPrototypeOf(conditionNode, UAAcknowledgeableConditionImpl.prototype);
        // ----------------------- Install Acknowledge-able Condition stuff
        // install ackedState - Mandatory
        /**
         * @property ackedState
         * @type TwoStateVariable
         */
        (0, ua_two_state_variable_1._install_TwoStateVariable_machinery)(conditionNode.ackedState, {
            falseState: "Unacknowledged",
            trueState: "Acknowledged"
        });
        /**
         * @property acknowledge
         * @type UAMethod
         */
        conditionNode.acknowledge.bindMethod(_acknowledge_method);
        // install confirmedState - Optional
        /**
         * @property confirmedState
         * @type TwoStateVariable
         */
        if (conditionNode.confirmedState) {
            (0, ua_two_state_variable_1._install_TwoStateVariable_machinery)(conditionNode.confirmedState, {
                falseState: "Unconfirmed",
                trueState: "Confirmed"
            });
        }
        // install confirm Method - Optional
        /**
         * @property confirm
         * @type UAMethod
         */
        if (conditionNode.confirm) {
            conditionNode.confirm.bindMethod(_confirm_method);
        }
        (0, node_opcua_assert_1.assert)(conditionNode instanceof UAAcknowledgeableConditionImpl);
        return conditionNode;
    }
    static install_method_handle_on_type(addressSpace) {
        const acknowledgeableConditionType = addressSpace.findEventType("AcknowledgeableConditionType");
        (0, node_opcua_assert_1.assert)(acknowledgeableConditionType !== null);
        acknowledgeableConditionType.acknowledge.bindMethod(_acknowledge_method);
        acknowledgeableConditionType.confirm?.bindMethod(_confirm_method);
    }
    _raiseAuditConditionAcknowledgeEvent(branch) {
        // raise the AuditConditionAcknowledgeEventType
        const eventData = {
            actionTimeStamp: { dataType: node_opcua_variant_1.DataType.DateTime, value: new Date() },
            // xx branchId: branch.branchId.readValue().value,
            // AuditEventType
            clientAuditEntryId: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            clientUserId: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            // The ConditionEventId field shall contain the id of the event for which the comment was added
            conditionEventId: { dataType: node_opcua_variant_1.DataType.ByteString, value: branch.getEventId() },
            // The Comment contains the actual comment that was added
            comment: { dataType: node_opcua_variant_1.DataType.LocalizedText, value: branch.getComment() },
            inputArguments: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            methodId: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            serverId: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            status: {
                dataType: node_opcua_variant_1.DataType.StatusCode,
                value: node_opcua_status_code_1.StatusCodes.Good
            }
        };
        this.raiseEvent("AuditConditionAcknowledgeEventType", eventData);
    }
    _raiseAuditConditionConfirmEvent(branch) {
        // raise the AuditConditionConfirmEventType
        const eventData = {
            actionTimeStamp: { dataType: node_opcua_variant_1.DataType.DateTime, value: new Date() },
            // ConditionEventId The ConditionEventId field shall contain the id of the Event that was confirmed
            conditionEventId: { dataType: node_opcua_variant_1.DataType.ByteString, value: branch.getEventId() },
            // xx branchId: branch.branchId.readValue().value,
            // AuditEventType
            clientAuditEntryId: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            clientUserId: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            comment: { dataType: node_opcua_variant_1.DataType.LocalizedText, value: branch.getComment() },
            inputArguments: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            methodId: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            serverId: {
                dataType: node_opcua_variant_1.DataType.Null
            },
            status: {
                dataType: node_opcua_variant_1.DataType.StatusCode,
                value: node_opcua_status_code_1.StatusCodes.Good
            }
        };
        this.raiseEvent("AuditConditionConfirmEventType", eventData);
    }
    _acknowledge_branch(conditionEventId, comment, branch, message) {
        (0, node_opcua_assert_1.assert)(typeof message === "string");
        const statusCode = branch._setAckedState(true, conditionEventId, comment);
        if (statusCode.isNotGood()) {
            return statusCode;
        }
        if (this.confirmedState) {
            // alarm has a confirmed state !
            // we should be waiting for confirmation now
            branch.setConfirmedState(false);
            branch.setRetain(true);
        }
        else {
            branch.setRetain(false);
        }
        branch.setComment(comment);
        this.raiseNewBranchState(branch);
        this._raiseAuditConditionAcknowledgeEvent(branch);
        /**
         * @event acknowledged
         * @param  eventId   {Buffer|null}
         * @param  comment   {LocalizedText}
         * @param  branch    {ConditionSnapshot}
         * raised when the alarm branch has been acknowledged
         */
        this.emit("acknowledged", conditionEventId, comment, branch);
        return node_opcua_status_code_1.StatusCodes.Good;
    }
    /**
     * @param conditionEventId The ConditionEventId field shall contain the id of the Event that was conformed
     * @private
     */
    _confirm_branch(conditionEventId, comment, branch, message) {
        (0, node_opcua_assert_1.assert)(typeof message === "string");
        (0, node_opcua_assert_1.assert)(comment instanceof node_opcua_data_model_1.LocalizedText);
        // xx var eventId = branch.getEventId();
        (0, node_opcua_assert_1.assert)(branch.getEventId().toString("hex") === conditionEventId.toString("hex"));
        branch.setConfirmedState(true);
        // once confirmed a branch do not need to be retained
        branch.setRetain(false);
        branch.setComment(comment);
        this._raiseAuditConditionCommentEvent(message, conditionEventId, comment);
        this._raiseAuditConditionConfirmEvent(branch);
        this.raiseNewBranchState(branch);
        /**
         * @event confirmed
         * @param  eventId
         * @param  comment
         * @param  eventId
         * raised when the alarm branch has been confirmed
         */
        this.emit("confirmed", conditionEventId, comment, branch);
    }
    /**
     */
    autoConfirmBranch(branch, comment) {
        (0, node_opcua_assert_1.assert)(branch instanceof condition_snapshot_impl_1.ConditionSnapshotImpl);
        if (!this.confirmedState) {
            // no confirmedState => ignoring
            return;
        }
        (0, node_opcua_assert_1.assert)(!branch.getConfirmedState(), "already confirmed ?");
        const conditionEventId = branch.getEventId();
        // istanbul ignore next
        doDebug && debugLog("autoConfirmBranch getAckedState ", branch.getAckedState());
        this._confirm_branch(conditionEventId, comment, branch, "Server/Confirm");
    }
    /**
     *
     */
    acknowledgeAndAutoConfirmBranch(branch, comment) {
        comment = node_opcua_data_model_1.LocalizedText.coerce(comment);
        const conditionEventId = branch.getEventId();
        branch.setRetain(false);
        this._acknowledge_branch(conditionEventId, comment, branch, "Server/Acknowledge");
        this.autoConfirmBranch(branch, comment);
    }
}
exports.UAAcknowledgeableConditionImpl = UAAcknowledgeableConditionImpl;
function _acknowledge_method(inputArguments, context, callback) {
    ua_condition_impl_1.UAConditionImpl.with_condition_method(inputArguments, context, callback, (conditionEventId, comment, branch, conditionNode) => {
        const ackConditionNode = conditionNode;
        // precondition checking
        (0, node_opcua_assert_1.assert)(!conditionEventId || conditionEventId instanceof Buffer, "must have a valid eventId or  null");
        (0, node_opcua_assert_1.assert)(comment instanceof node_opcua_data_model_1.LocalizedText, "expecting a comment as LocalizedText");
        (0, node_opcua_assert_1.assert)(conditionNode instanceof UAAcknowledgeableConditionImpl);
        ackConditionNode._acknowledge_branch(conditionEventId, comment, branch, "Method/Acknowledged");
        return node_opcua_status_code_1.StatusCodes.Good;
    });
}
/*
 *
 * param inputArguments {Variant[]}
 * param context        {Object}
 * param callback       {Function}
 *
 * @private
 */
function _confirm_method(inputArguments, context, callback) {
    ua_condition_impl_1.UAConditionImpl.with_condition_method(inputArguments, context, callback, (eventId, comment, branch, conditionNode) => {
        (0, node_opcua_assert_1.assert)(eventId instanceof Buffer);
        (0, node_opcua_assert_1.assert)(branch.getEventId() instanceof Buffer);
        (0, node_opcua_assert_1.assert)(branch.getEventId().toString("hex") === eventId.toString("hex"));
        const ackConditionNode = conditionNode;
        if (branch.getConfirmedState()) {
            return node_opcua_status_code_1.StatusCodes.BadConditionBranchAlreadyConfirmed;
        }
        ackConditionNode._confirm_branch(eventId, comment, branch, "Method/Confirm");
        return node_opcua_status_code_1.StatusCodes.Good;
    });
}
//# sourceMappingURL=ua_acknowledgeable_condition_impl.js.map