import { UInt16 } from "node-opcua-basic-types";
import { LocalizedText } from "node-opcua-data-model";
import { StatusCode } from "node-opcua-status-code";
import { ConditionInfo, ConditionInfoOptions } from "../../source/interfaces/alarms_and_conditions/condition_info_i";
/**
 * @private
 */
export declare class ConditionInfoImpl implements ConditionInfo {
    message: LocalizedText | null;
    quality: StatusCode | null;
    severity: UInt16 | null;
    retain: boolean | null;
    constructor(options: ConditionInfoOptions);
    /**
     *
     */
    isDifferentFrom(otherConditionInfo: ConditionInfo): boolean;
}
