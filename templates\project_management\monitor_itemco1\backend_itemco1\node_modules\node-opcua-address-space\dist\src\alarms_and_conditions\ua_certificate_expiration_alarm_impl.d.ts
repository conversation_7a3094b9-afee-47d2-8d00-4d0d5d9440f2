/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
import { Certificate } from "node-opcua-crypto/web";
import { DateTime } from "node-opcua-basic-types";
import { NodeId } from "node-opcua-nodeid";
import { DataType } from "node-opcua-variant";
import { INamespace, UAObject, UAProperty } from "node-opcua-address-space-base";
import { UACertificateExpirationAlarmEx } from "../../source/interfaces/alarms_and_conditions/ua_certificate_expiration_alarm_ex";
import { InstantiateOffNormalAlarmOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_off_normal_alarm_options";
import { UASystemOffNormalAlarmImpl } from "./ua_system_off_normal_alarm_impl";
export declare function instantiateCertificateExpirationAlarm(namespace: INamespace, alarmType: "CertificateExpirationAlarmType", options: InstantiateOffNormalAlarmOptions): UACertificateExpirationAlarmEx;
interface UACertificateExpirationAlarmImpl {
    expirationDate: UAProperty<Date, /*z*/ DataType.DateTime>;
    expirationLimit?: UAProperty<number, /*z*/ DataType.Double>;
    certificateType: UAProperty<NodeId, /*z*/ DataType.NodeId>;
    certificate: UAProperty<Buffer, /*z*/ DataType.ByteString>;
}
export declare const OneDayDuration: number;
export declare const TwoWeeksDuration: number;
/**
 * This UACertificateExpirationAlarm (SystemOffNormalAlarmType) is raised by the Server when the Server’s
 * Certificate is within the ExpirationLimit
 * of expiration. This alarm automatically returns to normal when the certificate is updated.
 */
declare class UACertificateExpirationAlarmImpl extends UASystemOffNormalAlarmImpl implements UACertificateExpirationAlarmEx {
    private timer;
    static instantiate(namespace: INamespace, alarmType: "CertificateExpirationAlarmType", options: InstantiateOffNormalAlarmOptions): UACertificateExpirationAlarmImpl;
    getExpirationDate(): DateTime | null;
    updateAlarmState2(isActive: boolean, severity: number, message: string): void;
    update(): void;
    private _updateAlarm;
    setExpirationDate(expirationDate: Date): void;
    getExpirationLimit(): number;
    setExpirationLimit(value: number): void;
    getCertificate(): Certificate | null;
    private _extractAndSetExpiryDate;
    setCertificate(certificate: Certificate | null): void;
    stopTimer(): void;
    _post_initialize(): void;
}
export declare function promoteToCertificateExpirationAlarm(node: UAObject): UACertificateExpirationAlarmImpl;
export {};
