"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwoWeeksDuration = exports.OneDayDuration = void 0;
exports.instantiateCertificateExpirationAlarm = instantiateCertificateExpirationAlarm;
exports.promoteToCertificateExpirationAlarm = promoteToCertificateExpirationAlarm;
/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
const web_1 = require("node-opcua-crypto/web");
const node_opcua_basic_types_1 = require("node-opcua-basic-types");
const node_opcua_debug_1 = require("node-opcua-debug");
const node_opcua_variant_1 = require("node-opcua-variant");
const node_opcua_constants_1 = require("node-opcua-constants");
const node_opcua_data_model_1 = require("node-opcua-data-model");
const register_node_promoter_1 = require("../../source/loader/register_node_promoter");
const ua_system_off_normal_alarm_impl_1 = require("./ua_system_off_normal_alarm_impl");
const warningLog = (0, node_opcua_debug_1.make_warningLog)("AlarmsAndConditions");
const ellipsis = (arg0, arg1 = 4) => {
    arg1 = Math.max(arg1, 4);
    return arg0.length <= arg1 ? arg0 : arg0.slice(0, arg1 / 2) + "..." + arg0.slice(arg0.length - arg1 / 2);
};
const d = (d) => {
    return d.toISOString();
};
function instantiateCertificateExpirationAlarm(namespace, alarmType, options) {
    return UACertificateExpirationAlarmImpl.instantiate(namespace, alarmType, options);
}
// This Simple DataType is a Double that defines an interval of time in milliseconds (fractions can be used to define sub-millisecond values).
// Negative values are generally invalid but may have special meanings where the Duration is used.
exports.OneDayDuration = 1000 * 60 * 60 * 24;
exports.TwoWeeksDuration = exports.OneDayDuration * 2 * 7;
/**
 * This UACertificateExpirationAlarm (SystemOffNormalAlarmType) is raised by the Server when the Server’s
 * Certificate is within the ExpirationLimit
 * of expiration. This alarm automatically returns to normal when the certificate is updated.
 */
class UACertificateExpirationAlarmImpl extends ua_system_off_normal_alarm_impl_1.UASystemOffNormalAlarmImpl {
    constructor() {
        super(...arguments);
        this.timer = null;
    }
    static instantiate(namespace, alarmType, options
    // data?: Record<string, VariantOptions>
    ) {
        const alarm = ua_system_off_normal_alarm_impl_1.UASystemOffNormalAlarmImpl.instantiate(namespace, alarmType || "CertificateExpirationAlarmType", options
        // data
        );
        promoteToCertificateExpirationAlarm(alarm);
        return alarm;
    }
    getExpirationDate() {
        return this.getChildByName("ExpirationDate")?.readValue().value.value;
    }
    updateAlarmState2(isActive, severity, message) {
        isActive ? this.activateAlarm() : this.deactivateAlarm();
        this.raiseNewCondition({
            message,
            quality: node_opcua_basic_types_1.StatusCodes.Good,
            retain: isActive ? true : false,
            severity
        });
    }
    update() {
        this._updateAlarm();
    }
    _updateAlarm() {
        const expirationDate = this.getExpirationDate();
        const now = new Date();
        const expirationLimit = this.getExpirationLimit();
        const checkDate = new Date(now.getTime() + +expirationLimit);
        const certificate = this.getCertificate();
        if (!expirationDate || ((0, node_opcua_basic_types_1.isMinDate)(expirationDate) && !certificate)) {
            if (!this.currentBranch() || this.currentBranch().getActiveState()) {
                this.updateAlarmState2(true, 255, "certificate is missing");
            }
            return;
        }
        const thumbprint = ellipsis((0, web_1.makeSHA1Thumbprint)(this.getCertificate() || Buffer.alloc(0)).toString("hex"), 10);
        const info = `| end date: ${d(expirationDate)} | expirationLimit=${expirationLimit}|`;
        //
        if (expirationDate.getTime() <= checkDate.getTime()) {
            // also raise the event
            if (expirationDate.getTime() <= now.getTime()) {
                this.updateAlarmState2(true, 250, `certificate ${thumbprint} has expired ${info}`);
            }
            else {
                //             check--------------------+
                //       expiry---------------+         |
                //       today-----+          |         |
                //                 v          v         v
                // ----------------+----------+---------+----------+
                const t1 = checkDate.getTime() - now.getTime();
                const t2 = checkDate.getTime() - expirationDate.getTime();
                const severity = t1 === 0 ? 255 : Math.floor((t2 / t1) * 100) + 100;
                this.updateAlarmState2(true, severity, `certificate ${thumbprint} is about to expire ${info}`);
            }
        }
        else {
            this.updateAlarmState2(false, 0, `certificate ${thumbprint} is OK! ${info}`);
        }
    }
    setExpirationDate(expirationDate) {
        this.expirationDate.setValueFromSource({
            dataType: node_opcua_variant_1.DataType.DateTime,
            value: expirationDate
        });
        this._updateAlarm();
    }
    getExpirationLimit() {
        // This shall be a positive number. If the property is not provided, a default of 2 weeks shall be used.
        if (!this.expirationLimit) {
            return exports.TwoWeeksDuration;
        }
        const dataValue = this.expirationLimit.readValue();
        if (dataValue.dataType === node_opcua_variant_1.DataType.Null) {
            return exports.TwoWeeksDuration;
        }
        return this.expirationLimit?.readValue().value.value || 0;
    }
    setExpirationLimit(value) {
        this.expirationLimit?.setValueFromSource({
            dataType: node_opcua_variant_1.DataType.Double,
            value
        });
        this._updateAlarm();
    }
    getCertificate() {
        return this.getChildByName("Certificate")?.readValue().value.value || null;
    }
    _extractAndSetExpiryDate(certificate) {
        if (certificate && certificate.length > 0) {
            const info = (0, web_1.exploreCertificate)(certificate);
            if (info.tbsCertificate.validity.notAfter) {
                this.setExpirationDate(info.tbsCertificate.validity.notAfter);
            }
            else {
                this.setExpirationDate((0, node_opcua_basic_types_1.getMinOPCUADate)());
            }
        }
        else {
            this.setExpirationDate((0, node_opcua_basic_types_1.getMinOPCUADate)());
        }
    }
    setCertificate(certificate) {
        this.certificate.setValueFromSource({
            dataType: node_opcua_variant_1.DataType.ByteString,
            value: certificate
        });
        this._extractAndSetExpiryDate(certificate);
    }
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    _post_initialize() {
        if (this.expirationLimit) {
            this.expirationLimit.accessLevel = (0, node_opcua_data_model_1.makeAccessLevelExFlag)("CurrentRead | CurrentWrite");
            this.expirationLimit.userAccessLevel = (0, node_opcua_data_model_1.makeAccessLevelExFlag)("CurrentRead | CurrentWrite");
            this.expirationLimit.on("value_changed", (dataValue) => {
                // make sure we re-evaluate the certificate
                const certificate = this.getCertificate();
                this.setCertificate(certificate);
            });
        }
        const certificate = this.getCertificate();
        this._extractAndSetExpiryDate(certificate);
        this.addressSpace.registerShutdownTask(() => {
            this.stopTimer();
        });
        this.timer = setInterval(() => this.update(), exports.OneDayDuration / 48);
    }
}
function promoteToCertificateExpirationAlarm(node) {
    if (node instanceof UACertificateExpirationAlarmImpl) {
        return node; // already promoted
    }
    Object.setPrototypeOf(node, UACertificateExpirationAlarmImpl.prototype);
    const _node = node;
    _node._post_initialize();
    return _node;
}
(0, register_node_promoter_1.registerNodePromoter)(node_opcua_constants_1.ObjectTypeIds.CertificateExpirationAlarmType, promoteToCertificateExpirationAlarm, true);
//# sourceMappingURL=ua_certificate_expiration_alarm_impl.js.map