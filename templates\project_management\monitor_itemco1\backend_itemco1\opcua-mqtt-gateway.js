const opcua = require("node-opcua");
const mqtt = require("mqtt");

const endpointUrl = "opc.tcp://************:4840";
const mqttClient = mqtt.connect("mqtt://broker.hivemq.com");
const mqttTopic = "plc/tben02/data";

const nodeList = [
  //monitor
  { tag: "pipePressure", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.pipePressure" },
  { tag: "tankLevel", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.levelSensor" },
  //step delivery
  { tag: "hmiParkingBrake", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiParkingBrake" },
  { tag: "hmiTicketPrinterReady", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiTicketPrinterReady" },
  { tag: "hmiPTO", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiPTO" },
  { tag: "hmiConnectBondingCab", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiConnectBondingCab" },
  { tag: "hmiMeterSignal", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiMeterSignal" },
  { tag: "hmiOpenButterflyValve", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiOpenButterflyValve" },
  { tag: "oneOutletOnly", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.oneOutletOnly" },
  { tag: "hmiRPM", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiRPM" },
  //stop delivery
  { tag: "dpLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.differentPressureLight" },
  { tag: "waterLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.waterLight" },
  { tag: "truckShutdown", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.truckShutDown" },
  { tag: "deadmanOvertime", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.deadmanOverTime" },
  { tag: "overrideLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.overrideLight" },
  //control panel
  { tag: "fuelDefuel", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiFDFSwitch" },
  { tag: "rewardHoseReel", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.rewardHoseReel" },
  { tag: "fordwardHoseReel", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.fordwardHoseReel" },
  //input
  { tag: "overrideButton", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.overrideButton" },
  { tag: "cabinetDoor", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.cabinetDoor" },
  { tag: "parkingBrakeSignal", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.parkingBrakeSignal" },
  { tag: "platformDoor", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.platformDoorSensor" },
  { tag: "joystickUp", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.upSwitch" },
  { tag: "joystickDown", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.downSwitch" },
  { tag: "limitUp", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.limitUpperLift" },
  { tag: "limitDown", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.interlockPlatformDownSensor" },
  { tag: "connerSenor", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.connerSensor" },
  { tag: "interlockNozzle", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.interlockNozzlePlatform" },
  { tag: "overfillSensor", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.overfillSensor" },
  { tag: "groundInterlock", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.interlockNozzleGround" },
  { tag: "waterInFilter", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.waterInFilter" },
  { tag: "dpSensor", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.differentPressure" },
  { tag: "emergencyButton", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.esd" },
  { tag: "deadmanButton", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.deadman" },
  //output
  { tag: "parkingBrakeValve", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.brakeValve" },
  { tag: "interlockLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.interlockLight" },
  { tag: "overrideLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.overrideLight" },
  { tag: "overfillBuzzer", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.alarmPLC" },
  { tag: "hoseReelWind", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.fordwardHoseReel" },
  { tag: "hoseReelReWind", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.rewardHoseReel" },
  { tag: "deadmanLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.deadmanLight" },
  { tag: "ptoPumpLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.hmiPumpPTOLight" },
  { tag: "dpLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.differentPressureLight" },
  { tag: "waterLight", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.waterLight" },
  //maintenance
  { tag: "timeCheckJet", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.timeCheckJet" },
  { tag: "timeCheckTank", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.timeCheckTank" },
  { tag: "timeCleanTank", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.timeCleanedTank" },
  { tag: "timeReplaceJet", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.timeReplacedJet" },
  { tag: "timeStartJet", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.timeStartJet" },
  { tag: "timeStartTank", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.timeStartTank" },
  { tag: "remainingCheckJet", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.remainingCheckJet" },
  { tag: "remainingCheckTank", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.remainingCheckTank" },
  { tag: "remainingCleanedTank", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.remainingCleanedTank" },
  { tag: "remainingReplacedJet", nodeId: "ns=4;s=|var|TBEN-L4-PLC-11.Application.PLC_PRG.remainingReplacedJet" },
];

const client = opcua.OPCUAClient.create({});
let session;

async function connectAndPublish() {
  try {
    await client.connect(endpointUrl);
    session = await client.createSession();
    console.log("✅ OPC UA connected!");

    setInterval(async () => {
      try {
        const nodesToRead = nodeList.map(n => ({
          nodeId: n.nodeId,
          attributeId: opcua.AttributeIds.Value
        }));

        const dataValues = await session.read(nodesToRead);

        const payload = nodeList.map((n, i) => ({
          tag: n.tag,
          value: dataValues[i].value.value,
          timestamp: new Date().toISOString()
        }));

        const payloadJson = JSON.stringify(payload);
        console.log("📤 Gửi MQTT:", payloadJson);
        mqttClient.publish(mqttTopic, payloadJson);

      } catch (readErr) {
        console.error("❌ Lỗi đọc OPC UA:", readErr.message);
      }
    }, 2000);

  } catch (err) {
    console.error("❌ Lỗi kết nối OPC UA:", err.message);
  }
}

connectAndPublish();
