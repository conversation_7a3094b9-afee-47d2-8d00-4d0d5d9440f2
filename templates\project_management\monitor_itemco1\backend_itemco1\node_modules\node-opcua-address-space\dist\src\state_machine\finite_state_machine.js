"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UAStateMachineTypeImpl = exports.UAStateMachineImpl = exports.UATransitionImpl = void 0;
exports.getFiniteStateMachineTypeStates = getFiniteStateMachineTypeStates;
exports.getFiniteStateMachineTypeTransitions = getFiniteStateMachineTypeTransitions;
exports.getFiniteStateMachineTypeStateByName = getFiniteStateMachineTypeStateByName;
exports.promoteToStateMachine = promoteToStateMachine;
exports.promoteToStateMachineType = promoteToStateMachineType;
/**
 * @module node-opcua-address-space
 */
const chalk_1 = __importDefault(require("chalk"));
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_constants_1 = require("node-opcua-constants");
const node_opcua_data_model_1 = require("node-opcua-data-model");
const node_opcua_data_model_2 = require("node-opcua-data-model");
const node_opcua_debug_1 = require("node-opcua-debug");
const node_opcua_nodeid_1 = require("node-opcua-nodeid");
const node_opcua_status_code_1 = require("node-opcua-status-code");
const node_opcua_variant_1 = require("node-opcua-variant");
const register_node_promoter_1 = require("../../source/loader/register_node_promoter");
const ua_object_impl_1 = require("../ua_object_impl");
const base_node_impl_1 = require("../base_node_impl");
const ua_object_type_impl_1 = require("../ua_object_type_impl");
const warningLog = (0, node_opcua_debug_1.make_warningLog)(__filename);
const doDebug = false;
const debugLog = (0, node_opcua_debug_1.make_debugLog)(__filename);
class UATransitionImpl {
}
exports.UATransitionImpl = UATransitionImpl;
function getComponentOfType(typeDef, typedefinition) {
    const get = (typeDef) => typeDef.getComponents().filter((cc) => {
        const c = cc;
        if (c.nodeClass !== node_opcua_data_model_1.NodeClass.Object)
            return false;
        if (!c.typeDefinitionObj || c.typeDefinitionObj.nodeClass !== node_opcua_data_model_1.NodeClass.ObjectType) {
            return false;
        }
        return c.typeDefinitionObj.isSubtypeOf(typedefinition);
    });
    let components_parts = get(typeDef);
    while (components_parts.length === 0 && typeDef.subtypeOfObj) {
        // there is no element of that type available in the top level
        typeDef = typeDef.subtypeOfObj;
        components_parts = get(typeDef);
    }
    return components_parts;
}
const defaultPredicate = (transitions, fromState, toState) => {
    if (transitions.length === 0) {
        return null;
    }
    if (transitions.length === 1) {
        return transitions[0];
    }
    warningLog(" FromState = ", fromState.browseName.toString());
    warningLog(" ToState   = ", toState.browseName.toString());
    for (const transition of transitions) {
        warningLog("  possible transition : ", transition.browseName.toString(), " ", transition.nodeId.toString());
    }
    warningLog("warning: a duplicated FromState Reference to the same target has been found.\nPlease check your model or provide a predicate method to select which one to use");
    warningLog("fromStateNode: ", fromState.toString());
    return transitions[0];
};
function getFiniteStateMachineTypeStates(uaFiniteStateMachineType) {
    const addressSpace = uaFiniteStateMachineType.addressSpace;
    const initialStateType = addressSpace.findObjectType("InitialStateType");
    // istanbul ignore next
    if (!initialStateType) {
        throw new Error("cannot find InitialStateType");
    }
    const stateType = addressSpace.findObjectType("StateType");
    // istanbul ignore next
    if (!stateType) {
        throw new Error("cannot find StateType");
    }
    (0, node_opcua_assert_1.assert)(initialStateType.isSubtypeOf(stateType));
    const comp = getComponentOfType(uaFiniteStateMachineType, stateType);
    return comp;
}
function getFiniteStateMachineTypeTransitions(uaFiniteStateMachineType) {
    const addressSpace = uaFiniteStateMachineType.addressSpace;
    const transitionType = addressSpace.findObjectType("TransitionType");
    // istanbul ignore next
    if (!transitionType) {
        throw new Error("cannot find TransitionType");
    }
    const comp = getComponentOfType(uaFiniteStateMachineType, transitionType);
    return comp;
}
function getFiniteStateMachineTypeStateByName(uaFiniteStateMachineType, stateName) {
    let states = getFiniteStateMachineTypeStates(uaFiniteStateMachineType);
    states = states.filter((s) => {
        return s.browseName.name === stateName;
    });
    (0, node_opcua_assert_1.assert)(states.length <= 1);
    return states.length === 1 ? states[0] : null;
}
/*
 *
 * @class StateMachine
 *
 */
class UAStateMachineImpl extends ua_object_impl_1.UAObjectImpl {
    getStates() {
        const typeDef = this.typeDefinitionObj;
        return getFiniteStateMachineTypeStates(typeDef);
    }
    get states() {
        return this.getStates();
    }
    /**
     * @param name  the name of the state to get
     * @return the state with the given name
     */
    getStateByName(name) {
        return getFiniteStateMachineTypeStateByName(this.typeDefinitionObj, name);
    }
    getTransitions() {
        const typeDef = this.typeDefinitionObj;
        return getFiniteStateMachineTypeTransitions(typeDef);
    }
    get transitions() {
        return this.getTransitions();
    }
    /**
     * return the node InitialStateType
     * @property initialState
     */
    get initialState() {
        const addressSpace = this.addressSpace;
        const initialStateType = addressSpace.findObjectType("InitialStateType");
        const typeDef = this.typeDefinitionObj;
        const comp = getComponentOfType(typeDef, initialStateType);
        // istanbul ignore next
        if (comp.length > 1) {
            throw new Error(" More than 1 initial state in stateMachine");
        }
        return comp.length === 0 ? null : comp[0];
    }
    /**
     *
     * @param node
     * @private
     */
    _coerceNode(node) {
        if (node instanceof base_node_impl_1.BaseNodeImpl) {
            return node;
        }
        else if (node instanceof node_opcua_nodeid_1.NodeId) {
            const addressSpace = this.addressSpace;
            return addressSpace.findNode(node);
        }
        else if (typeof node === "string") {
            return this.getStateByName(node);
        }
        return null;
    }
    /**
     */
    isValidTransition(toStateNode, predicate) {
        // is it legal to go from state currentState to toStateNode;
        if (!this.currentStateNode) {
            return true;
        }
        const n = this.currentState.readValue();
        // to be executed there must be a transition from currentState to toState
        const transition = this.findTransitionNode(this.currentStateNode, toStateNode, predicate);
        if (!transition) {
            // istanbul ignore next
            if (doDebug) {
                debugLog(" No transition from ", this.currentStateNode.browseName.toString(), " to ", toStateNode.toString());
            }
            return false;
        }
        return true;
    }
    /**
     */
    findTransitionNode(fromStateNode, toStateNode, predicate) {
        const addressSpace = this.addressSpace;
        const _fromStateNode = this._coerceNode(fromStateNode);
        if (!_fromStateNode) {
            return null;
        }
        const _toStateNode = this._coerceNode(toStateNode);
        if (!_toStateNode) {
            return null;
        }
        // istanbul ignore next
        if (_fromStateNode.nodeClass !== node_opcua_data_model_1.NodeClass.Object) {
            throw new Error("Internal Error");
        }
        // istanbul ignore next
        if (_toStateNode && _toStateNode.nodeClass !== node_opcua_data_model_1.NodeClass.Object) {
            throw new Error("Internal Error");
        }
        const stateType = addressSpace.findObjectType("StateType");
        // istanbul ignore next
        if (!stateType) {
            throw new Error("Cannot find StateType");
        }
        (0, node_opcua_assert_1.assert)(_fromStateNode.typeDefinitionObj.isSubtypeOf(stateType));
        (0, node_opcua_assert_1.assert)(_toStateNode.typeDefinitionObj.isSubtypeOf(stateType));
        let transitions = _fromStateNode.findReferencesAsObject("FromState", false);
        transitions = transitions.filter((transition) => {
            (0, node_opcua_assert_1.assert)(transition.toStateNode.nodeClass === node_opcua_data_model_1.NodeClass.Object);
            return transition.toStateNode === _toStateNode;
        });
        if (transitions.length === 0) {
            // cannot find a transition from fromState to toState
            return null;
        }
        // istanbul ignore next
        if (transitions.length > 1) {
            const selectedTransition = (predicate || defaultPredicate)(transitions, _fromStateNode, _toStateNode);
            return selectedTransition;
        }
        return transitions[0];
    }
    get currentStateNode() {
        return this._currentStateNode;
    }
    /**
     * @property currentStateNode
     * @type BaseNode
     */
    set currentStateNode(value) {
        this._currentStateNode = value;
    }
    /**
     */
    getCurrentState() {
        // xx this.currentState.readValue().value.value.text
        // xx this.shelvingState.currentStateNode.browseName.toString()
        if (!this.currentStateNode) {
            return null;
        }
        return this.currentStateNode.browseName.toString();
    }
    /**
     */
    setState(toStateNode, predicate) {
        if (!toStateNode) {
            this.currentStateNode = null;
            this.currentState.setValueFromSource({ dataType: node_opcua_variant_1.DataType.LocalizedText, value: null }, node_opcua_status_code_1.StatusCodes.BadStateNotActive);
            return;
        }
        if (typeof toStateNode === "string") {
            const state = this.getStateByName(toStateNode);
            // istanbul ignore next
            if (!state) {
                throw new Error("Cannot find state with name " + toStateNode);
            }
            toStateNode = state;
        }
        const fromStateNode = this.currentStateNode;
        toStateNode = this._coerceNode(toStateNode);
        (0, node_opcua_assert_1.assert)(toStateNode.nodeClass === node_opcua_data_model_1.NodeClass.Object);
        this.currentState.setValueFromSource({
            dataType: node_opcua_variant_1.DataType.LocalizedText,
            value: (0, node_opcua_data_model_1.coerceLocalizedText)(toStateNode.displayName[0] || toStateNode.browseName.name)
        }, node_opcua_status_code_1.StatusCodes.Good);
        this.currentStateNode = toStateNode;
        const applyCurrentStateOptionalProps = () => {
            const uaId = this.currentState.getPropertyByName("Id");
            if (uaId) {
                uaId.setValueFromSource({
                    dataType: node_opcua_variant_1.DataType.NodeId,
                    value: this.currentStateNode ? this.currentStateNode.nodeId : node_opcua_nodeid_1.NodeId.nullNodeId
                });
            }
            const uaName = this.currentState.getPropertyByName("Name");
            if (uaName) {
                uaName.setValueFromSource({
                    dataType: node_opcua_variant_1.DataType.QualifiedName,
                    value: this.currentStateNode ? this.currentStateNode.browseName : (0, node_opcua_data_model_1.coerceQualifiedName)("")
                });
            }
            const uaNumber = this.currentState.getPropertyByName("Number");
            if (uaNumber) {
                if (!this.currentStateNode) {
                    const n = uaNumber.setValueFromSource({
                        dataType: node_opcua_variant_1.DataType.UInt32,
                        value: 0 // may be wrong !
                    });
                }
                else {
                    const n = uaNumber.setValueFromSource({
                        dataType: node_opcua_variant_1.DataType.UInt32,
                        value: this.currentStateNode.stateNumber.readValue().value.value
                    });
                }
            }
            const uaEffectiveDisplayName = this.currentState.getPropertyByName("EffectiveDisplayName");
            if (uaEffectiveDisplayName) {
                uaEffectiveDisplayName.setValueFromSource({
                    dataType: node_opcua_variant_1.DataType.LocalizedText,
                    value: (0, node_opcua_data_model_1.coerceLocalizedText)(this.currentStateNode ? this.currentStateNode.displayName[0] || this.currentStateNode.browseName.name : "")
                });
            }
        };
        applyCurrentStateOptionalProps();
        const transitionTime = new Date();
        const applyTransitionOptionalPropsPhase1 = () => {
            //                                                  +-----------------------------+
            //             +--------------------------------|>|>| MainStateMachineType        |
            //             |                                    +-----------------------------+
            //             |                                                 |
            //             |                                                 |    +--------+
            // +------------------+                                          +--->| State1 |
            // | MainStateMachine |                                          |    +--------+
            // +------------------+                                          |
            //       |                                                       |    +--------+
            //       |       +----------------+                              +--->| State2 |   --- HasSubStateMachine --+
            //       +---||->| SubStateMachine|                              |    +--------+                            |
            //               +----------------+                              |                                          |
            //                      |                                        |    +-----------------+                   |
            //                                                               +--->| SubStateMachine |<------------------+
            //                                                                    +-----------------+
            //
            // * `this` is potentially a subState machine
            // *  get mainState = parent of `this`
            // *  is mainState.typeDefinition is not StateMachineType -> exit
            // *  find subMachine in mainState.typeDefinition (or any subtype of it) that has the same browse name as this.browseName
            const ms = this.findReferencesExAsObject("Aggregates", node_opcua_data_model_1.BrowseDirection.Inverse)[0];
            if (!ms)
                return;
            if (ms.nodeClass !== node_opcua_data_model_1.NodeClass.Object)
                return;
            const stateMachineType = this.addressSpace.findObjectType("StateMachineType");
            if (!ms.typeDefinitionObj.isSubtypeOf(stateMachineType))
                return;
            const find = (node, browseName) => {
                const r = node
                    .getAggregates()
                    .filter((x) => x.browseName.name === browseName.name && x.namespaceIndex === browseName.namespaceIndex);
                if (r.length === 0) {
                    // look in subType
                    const subType = node.subtypeOfObj;
                    if (subType) {
                        return find(subType, browseName);
                    }
                    return null;
                }
                else {
                    const retVal = r[0];
                    // istanbul ignore next
                    if (retVal.nodeClass !== node_opcua_data_model_1.NodeClass.Variable && retVal.nodeClass !== node_opcua_data_model_1.NodeClass.Object) {
                        throw new Error("find: expecting only object and variable here");
                    }
                    return retVal;
                }
            };
            const stateMachineInType = find(ms.typeDefinitionObj, this.browseName);
            if (!stateMachineInType)
                return;
            const mainState = stateMachineInType.findReferencesAsObject("HasSubStateMachine", false)[0];
            if (mainState) {
                if (!ms.currentStateNode || !(0, node_opcua_nodeid_1.sameNodeId)(mainState.nodeId, ms.currentStateNode.nodeId)) {
                    return;
                }
                // also update uaLastTransition.EffectiveTransitionTime
                const uaLastTransitionMain = ms.getComponentByName("LastTransition");
                if (uaLastTransitionMain) {
                    const uaEffectiveTransitionTimeMain = uaLastTransitionMain.getPropertyByName("EffectiveTransitionTime");
                    if (uaEffectiveTransitionTimeMain) {
                        uaEffectiveTransitionTimeMain.setValueFromSource({
                            dataType: node_opcua_variant_1.DataType.DateTime,
                            value: transitionTime
                        });
                    }
                }
            }
        };
        applyTransitionOptionalPropsPhase1();
        const transitionNode = this.findTransitionNode(fromStateNode, toStateNode, predicate);
        if (transitionNode) {
            const applyLastTransitionOptionalProps = () => {
                const uaLastTransition = this.getComponentByName("LastTransition");
                if (uaLastTransition) {
                    uaLastTransition.setValueFromSource({
                        dataType: node_opcua_variant_1.DataType.LocalizedText,
                        value: transitionNode.displayName[0] || transitionNode.browseName.name
                    }, node_opcua_status_code_1.StatusCodes.Good, transitionTime);
                    const uaId = uaLastTransition.getPropertyByName("Id");
                    if (uaId) {
                        uaId.setValueFromSource({
                            dataType: node_opcua_variant_1.DataType.NodeId,
                            value: transitionNode.nodeId
                        });
                    }
                    const uaLastTransitionTime = uaLastTransition.getPropertyByName("TransitionTime");
                    if (uaLastTransitionTime) {
                        uaLastTransitionTime.setValueFromSource({
                            dataType: node_opcua_variant_1.DataType.DateTime,
                            value: transitionTime
                        });
                    }
                    /**
                     * EffectiveTransitionTime specifies the time when the current state or one of its sub-states was entered.
                     * If, for example, a StateA is active and – while active – switches several times between its sub-states
                     * SubA and SubB, then the TransitionTime stays at the point in time where StateA became active whereas the *
                     * EffectiveTransitionTime changes with each change of a sub-state.
                     */
                    const uaEffectiveTransitionTime = uaLastTransition.getPropertyByName("EffectiveTransitionTime");
                    if (uaEffectiveTransitionTime) {
                        uaEffectiveTransitionTime.setValueFromSource({
                            dataType: node_opcua_variant_1.DataType.DateTime,
                            value: transitionTime
                        });
                    }
                    //
                    const uaName = uaLastTransition.getPropertyByName("Name");
                    if (uaName) {
                        uaName.setValueFromSource({
                            dataType: node_opcua_variant_1.DataType.QualifiedName,
                            value: transitionNode.browseName
                        });
                    }
                    const uaNumber = uaLastTransition.getPropertyByName("Number");
                    if (uaNumber) {
                        uaNumber.setValueFromSource({
                            dataType: node_opcua_variant_1.DataType.UInt32,
                            value: transitionNode.transitionNumber.readValue().value.value
                        });
                    }
                }
            };
            applyLastTransitionOptionalProps();
            // The inherited Property SourceNode shall be filled with the NodeId of the StateMachine instance where the
            // Transition occurs. If the Transition occurs in a SubStateMachine, then the NodeId of the SubStateMachine
            // has to be used. If the Transition occurs between a StateMachine and a SubStateMachine, then the NodeId of
            // the StateMachine has to be used, independent of the direction of the Transition.
            // Transition identifies the Transition that triggered the Event.
            // FromState identifies the State before the Transition.
            // ToState identifies the State after the Transition.
            this.raiseEvent("TransitionEventType", {
                // Base EventType
                // xx nodeId:      this.nodeId,
                // TransitionEventType
                // TransitionVariableType
                transition: {
                    dataType: "LocalizedText",
                    value: transitionNode.displayName[0]
                },
                "transition.id": transitionNode.transitionNumber.readValue().value,
                fromState: {
                    dataType: "LocalizedText",
                    value: fromStateNode ? fromStateNode.displayName[0] : ""
                }, // StateVariableType
                "fromState.id": fromStateNode
                    ? fromStateNode.stateNumber.readValue().value
                    : {
                        dataType: "Null"
                    },
                toState: {
                    dataType: "LocalizedText",
                    value: toStateNode.displayName[0]
                }, // StateVariableType
                "toState.id": toStateNode.stateNumber.readValue().value
            });
        }
        else {
            if (fromStateNode && fromStateNode !== toStateNode) {
                // istanbul ignore next
                if (doDebug) {
                    const f = fromStateNode.browseName.toString();
                    const t = toStateNode.browseName.toString();
                    warningLog(chalk_1.default.red("Warning"), " cannot raise event :  transition " + f + " to " + t + " is missing");
                }
            }
        }
        // also update executable flags on methods
        for (const method of this.getMethods()) {
            method._notifyAttributeChange(node_opcua_data_model_2.AttributeIds.Executable);
        }
    }
    /**
     * @internal
     * @private|
     */
    _post_initialize() {
        const addressSpace = this.addressSpace;
        const finiteStateMachineType = addressSpace.findObjectType("FiniteStateMachineType");
        // istanbul ignore next
        if (!finiteStateMachineType) {
            throw new Error("cannot find FiniteStateMachineType");
        }
        // xx assert(this.typeDefinitionObj && !this.subtypeOfObj);
        // xx assert(!this.typeDefinitionObj || this.typeDefinitionObj.isSubtypeOf(finiteStateMachineType));
        // get current Status
        const d = this.currentState.readValue();
        if (d.statusCode.isNotGood()) {
            this.setState(null);
        }
        else {
            const txt = d.value && d.value.value ? (d.value.value.text ? d.value.value.text.toString() : d.value.value.toString()) : "";
            this.currentStateNode = this.getStateByName(txt);
        }
        // Install AvailableStatesVariable if available
        const uaAvailableStates = this.getComponentByName("AvailableStates", 0);
        if (uaAvailableStates) {
            uaAvailableStates.bindVariable({
                get: () => {
                    return new node_opcua_variant_1.Variant({
                        arrayType: node_opcua_variant_1.VariantArrayType.Array,
                        dataType: node_opcua_variant_1.DataType.NodeId,
                        value: this.getStates().map((state) => state.nodeId)
                    });
                }
            }, true);
        }
        const uaAvailableTransitions = this.getComponentByName("AvailableTransitions", 0);
        if (uaAvailableTransitions) {
            uaAvailableTransitions.bindVariable({
                get: () => {
                    return new node_opcua_variant_1.Variant({
                        arrayType: node_opcua_variant_1.VariantArrayType.Array,
                        dataType: node_opcua_variant_1.DataType.NodeId,
                        value: this.getTransitions().map((state) => state.nodeId)
                    });
                }
            }, true);
        }
    }
}
exports.UAStateMachineImpl = UAStateMachineImpl;
function promoteToStateMachine(node) {
    if (node instanceof UAStateMachineImpl) {
        return node; // already promoted
    }
    Object.setPrototypeOf(node, UAStateMachineImpl.prototype);
    (0, node_opcua_assert_1.assert)(node instanceof UAStateMachineImpl, "should now  be a State Machine");
    const _node = node;
    _node._post_initialize();
    return _node;
}
(0, register_node_promoter_1.registerNodePromoter)(node_opcua_constants_1.ObjectTypeIds.FiniteStateMachineType, promoteToStateMachine);
class UAStateMachineTypeImpl extends ua_object_type_impl_1.UAObjectTypeImpl {
    getStateByName(name) {
        return getFiniteStateMachineTypeStateByName(this, name);
    }
    getStates() {
        return getFiniteStateMachineTypeStates(this);
    }
    getTransitions() {
        return getFiniteStateMachineTypeTransitions(this);
    }
    _post_initialize() {
        /** */
    }
}
exports.UAStateMachineTypeImpl = UAStateMachineTypeImpl;
function promoteToStateMachineType(node) {
    if (node instanceof UAStateMachineTypeImpl) {
        return node; // already promoted
    }
    Object.setPrototypeOf(node, UAStateMachineTypeImpl.prototype);
    (0, node_opcua_assert_1.assert)(node instanceof UAStateMachineTypeImpl, "should now  be a State Machine");
    const _node = node;
    _node._post_initialize();
    return _node;
}
//# sourceMappingURL=finite_state_machine.js.map