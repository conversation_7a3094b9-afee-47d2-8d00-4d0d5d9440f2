{"version": 3, "file": "ua_off_normal_alarm_impl.js", "sourceRoot": "", "sources": ["../../../src/alarms_and_conditions/ua_off_normal_alarm_impl.ts"], "names": [], "mappings": ";;;AAIA,yDAA2C;AAE3C,yDAAuD;AAGvD,uDAAqD;AACrD,2DAA8D;AAI9D,qEAA+D;AAE/D,SAAS,OAAO,CAAC,MAAW,EAAE,MAAW;IACrC,OAAO,MAAM,KAAK,MAAM,CAAC;AAC7B,CAAC;AA2BD;;;;;GAKG;AACH,MAAa,oBAAqB,SAAQ,4CAAmB;IACzD;;;OAGG;IACI,MAAM,CAAC,WAAW,CACrB,SAAqB,EACrB,gBAAiC,EACjC,OAAyC,EACzC,IAAqC;QAErC,MAAM,YAAY,GAAG,SAAS,CAAC,YAAmC,CAAC;QAEnE,MAAM,kBAAkB,GAAG,YAAY,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAC5E,0BAA0B;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QAED,IAAA,0BAAM,EAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,wBAAwB,CAAC,CAAC,CAAC,2BAA2B;QACzH,IAAA,0BAAM,EAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE,iCAAiC,CAAC,CAAC,CAAC,2BAA2B;QACpI,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;QAE5C,IAAA,0BAAM,EAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,wBAAwB,CAAC,CAAC,CAAC,2BAA2B;QACzH,MAAM,SAAS,GAAG,4CAAmB,CAAC,WAAW,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAyB,CAAC;QACtH,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAEjE;;;;;;;;WAQG;QACH,MAAM,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAsB,CAAC;QACnF,qFAAqF;QAErF,IAAI,SAAS,EAAE,CAAC;YACZ,+CAA+C;YAC/C,SAAS,CAAC,0BAA0B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC;QAED;;;;;;;;;WASG;QACH,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAsB,CAAC;YACvF,MAAM,iBAAiB,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,0BAAM,EAAE,CAAC;YAC1E,SAAS,CAAC,WAAW,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,6BAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;YAClG,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,YAAuB,CAAC,6BAA6B,EAAE,EAAE;gBAChG,4DAA4D;gBAC5D,mEAAmE;gBACnE,wDAAwD;gBACxD,WAAW;YACf,CAAC,CAAC,CAAC;YACH,IAAI,WAAW,EAAE,CAAC;gBACd,4CAA4C;gBAC5C,WAAW,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,YAAuB,CAAC,6BAA6B,EAAE,EAAE;oBACtF,SAAS,CAAC,6BAA6B,CAAC,YAAY,CAAC,CAAC;gBAC1D,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,SAAS,CAAC,WAAW,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,6BAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,0BAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QACtG,CAAC;QAED,SAAS,CAAC,uBAAuB,EAAE,CAAC;QAEpC,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,iEAAiE;IACjE,0FAA0F;IAC1F,wFAAwF;IACxF,+FAA+F;IAC/F,+FAA+F;IAC/F,6FAA6F;IAC7F,iGAAiG;IACjG,eAAe;IAER,kBAAkB;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;QACxD,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAyC,CAAC;QACxF,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;OACG;IACI,mBAAmB;QACtB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClD,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,eAAe,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,KAAe,CAAC;IAC7D,CAAC;IAED;OACG;IACI,mBAAmB,CAAC,KAAiB;QACxC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC3C,CAAC;IAEM,gBAAgB,CAAC,QAAiB,EAAE,OAAe;QACtD,IAAI,QAAQ,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC;YAC3C,wBAAwB;YACxB,OAAO;QACX,CAAC;QACD,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC;QACnD,uBAAuB;QACvB,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,IAAI,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,gBAAsB,EAAE,UAAgB;QACpE,IAAI,IAAA,oCAAiB,EAAC,gBAAgB,CAAC,IAAI,IAAA,oCAAiB,EAAC,UAAU,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACjC,OAAO;QACX,CAAC;QACD,MAAM,QAAQ,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;QACxD,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC;IAC1D,CAAC;IAES,uBAAuB,CAAC,SAAoB;QAClD,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;YACnC,qBAAqB;YACrB,OAAO;QACX,CAAC;QACD,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,KAAK,6BAAQ,CAAC,IAAI,EAAE,CAAC;YAC7C,qBAAqB;YACrB,OAAO;QACX,CAAC;QACD,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;QACzC,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACpD,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;IAES,6BAA6B,CAAC,SAAoB;QACxD,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;YACnC,qBAAqB;YACrB,OAAO;QACX,CAAC;QACD,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,KAAK,6BAAQ,CAAC,IAAI,EAAE,CAAC;YAC7C,qBAAqB;YACrB,OAAO;QACX,CAAC;QACD,MAAM,gBAAgB,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC5C,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC;CACJ;AAnKD,oDAmKC"}