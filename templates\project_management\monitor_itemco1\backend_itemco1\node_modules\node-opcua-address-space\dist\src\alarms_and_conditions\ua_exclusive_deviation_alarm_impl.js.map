{"version": 3, "file": "ua_exclusive_deviation_alarm_impl.js", "sourceRoot": "", "sources": ["../../../src/alarms_and_conditions/ua_exclusive_deviation_alarm_impl.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,yDAA2C;AAU3C,qEAKkC;AAElC,mFAA6E;AAC7E,+DAA0D;AAW1D,MAAa,6BAA8B,SAAQ,yDAAyB;IAGjE,MAAM,CAAC,WAAW,CACrB,SAA2B,EAC3B,IAAqB,EACrB,OAA8C,EAC9C,IAAqC;QAErC,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;QAE5C,MAAM,2BAA2B,GAAG,YAAY,CAAC,aAAa,CAAC,6BAA6B,CAAC,CAAC;QAC9F,0BAA0B;QAC1B,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAA,0BAAM,EAAC,IAAI,KAAK,2BAA2B,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEnE,MAAM,KAAK,GAAG,yDAAyB,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAkC,CAAC;QACrH,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,6BAA6B,CAAC,SAAS,CAAC,CAAC;QACtE,IAAA,0BAAM,EAAC,KAAK,YAAY,6BAA6B,CAAC,CAAC;QACvD,IAAA,0BAAM,EAAC,KAAK,YAAY,yDAAyB,CAAC,CAAC;QACnD,IAAA,0BAAM,EAAC,KAAK,YAAY,sCAAgB,CAAC,CAAC;QAE1C,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAEjC,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,mBAAmB;QACtB,OAAO,iEAAwC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;IAEM,gBAAgB;QACnB,OAAO,8DAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEM,0BAA0B,CAAC,SAAoB;QAClD,uEAA8C,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACzE,CAAC;IAEM,iBAAiB,CAAC,OAA+B;QACpD,OAAO,8DAAqC,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrE,CAAC;IAEM,0BAA0B,CAAC,KAAa;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QACD,IAAA,0BAAM,EAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;QAChC,iCAAiC;QACjC,yDAAyB,CAAC,SAAS,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,aAAa,CAAC,CAAC;IACrG,CAAC;CACJ;AAvDD,sEAuDC;AAID;;;;;GAKG"}