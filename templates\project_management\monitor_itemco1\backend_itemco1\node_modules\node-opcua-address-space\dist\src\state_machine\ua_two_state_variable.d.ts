import { LocalizedText, LocalizedTextLike } from "node-opcua-data-model";
import { DataValueT } from "node-opcua-data-value";
import { NodeId } from "node-opcua-nodeid";
import { StatusCode, StatusCodeCallback } from "node-opcua-status-code";
import { DataType } from "node-opcua-variant";
import { BaseNode, BindVariableOptions, INamespace, UAReference, UAVariable, ISessionContext } from "node-opcua-address-space-base";
import { NumericRange } from "node-opcua-numeric-range";
import { QualifiedNameLike } from "node-opcua-data-model";
import { AddTwoStateVariableOptions } from "../../source/address_space_ts";
import { UATwoStateVariableEx } from "../../source/ua_two_state_variable_ex";
import { UAVariableImplT } from "../ua_variable_impl";
import { ISetStateOptions } from "../../source/interfaces/i_set_state_options";
export declare function _install_TwoStateVariable_machinery(node: UAVariable, options: TwoStateVariableInitializeOptions): UATwoStateVariableEx;
export declare function promoteToTwoStateVariable(node: UAVariable): UATwoStateVariableEx;
export interface TwoStateVariableInitializeOptions {
    trueState?: LocalizedTextLike;
    falseState?: LocalizedTextLike;
    isFalseSubStateOf?: NodeId | string | BaseNode;
    isTrueSubStateOf?: NodeId | string | BaseNode;
    value?: boolean | BindVariableOptions;
}
export declare interface UATwoStateVariableImpl extends UATwoStateVariableEx {
    on(): this;
    once(): this;
    readValue(context?: ISessionContext | null, indexRange?: NumericRange, dataEncoding?: QualifiedNameLike | null): DataValueT<LocalizedText, DataType.LocalizedText>;
    writeValue(context: ISessionContext, dataValue: DataValueT<LocalizedText, DataType.LocalizedText>, indexRange: NumericRange | null, callback: StatusCodeCallback): void;
    writeValue(context: ISessionContext, dataValue: DataValueT<LocalizedText, DataType.LocalizedText>, callback: StatusCodeCallback): void;
    writeValue(context: ISessionContext, dataValue: DataValueT<LocalizedText, DataType.LocalizedText>, indexRange?: NumericRange | null): Promise<StatusCode>;
}
/***
 * @class UATwoStateVariable
 */
export declare class UATwoStateVariableImpl extends UAVariableImplT<LocalizedText, DataType.LocalizedText> implements UATwoStateVariableEx {
    private _trueState?;
    private _falseState?;
    constructor(opts: any);
    get isFalseSubStateOf(): UATwoStateVariableEx;
    get isTrueSubStateOf(): UATwoStateVariableEx;
    initialize(options: TwoStateVariableInitializeOptions): void;
    _postInitialize(): void;
    /**
     */
    setValue(boolValue: boolean, options?: ISetStateOptions): void;
    /**
     */
    getValue(): boolean;
    /**
     */
    getValueAsString(): string;
    getTrueState(): LocalizedText;
    getFalseState(): LocalizedText;
    _add_backward_reference(reference: UAReference): void;
}
export declare function _addTwoStateVariable(namespace: INamespace, options: AddTwoStateVariableOptions): UATwoStateVariableEx;
