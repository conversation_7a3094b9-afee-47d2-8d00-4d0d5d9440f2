"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UABaseEventImpl = void 0;
/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
const node_opcua_assert_1 = require("node-opcua-assert");
const node_opcua_variant_1 = require("node-opcua-variant");
const ua_object_impl_1 = require("../ua_object_impl");
/**
 * @internal
 */
class UABaseEventImpl extends ua_object_impl_1.UAObjectImpl {
    /**
     */
    setSourceName(name) {
        (0, node_opcua_assert_1.assert)(typeof name === "string");
        this.sourceName.setValueFromSource(new node_opcua_variant_1.Variant({
            dataType: node_opcua_variant_1.DataType.String,
            value: name
        }));
    }
    /**
     */
    setSourceNode(node) {
        this.sourceNode.setValueFromSource(new node_opcua_variant_1.Variant({
            dataType: node_opcua_variant_1.DataType.NodeId,
            value: node.nodeId ? node.nodeId : node
        }));
    }
}
exports.UABaseEventImpl = UABaseEventImpl;
//# sourceMappingURL=ua_base_event_impl.js.map