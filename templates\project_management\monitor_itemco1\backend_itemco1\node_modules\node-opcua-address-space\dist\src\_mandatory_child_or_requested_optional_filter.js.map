{"version": 3, "file": "_mandatory_child_or_requested_optional_filter.js", "sourceRoot": "", "sources": ["../../src/_mandatory_child_or_requested_optional_filter.ts"], "names": [], "mappings": ";;;AAAA,yDAA2C;AAC3C,iFAA8H;AAC9H,uDAAiG;AACjG,iEAAwD;AAIxD,8CAA8C;AAC9C,MAAM,OAAO,GAAG,IAAA,iCAAc,EAAC,UAAU,CAAC,CAAC;AAC3C,MAAM,UAAU,GAAG,IAAA,kCAAe,EAAC,UAAU,CAAC,CAAC;AAC/C,MAAM,QAAQ,GAAG,IAAA,gCAAa,EAAC,UAAU,CAAC,CAAC;AAC3C,MAAM,OAAO,GAAG,IAAA,iCAAc,EAAC,aAAa,CAAC,CAAC;AAC9C,MAAM,QAAQ,GAAG,QAAQ,CAAC;AAG1B,MAAa,uCAAuC;IAMhD,YAAY,QAAkB,EAAE,oBAA6B,EAAE,YAAyB;QAFvE,yBAAoB,GAAY,KAAK,CAAC;QAGnD,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QACjD,yEAAyE;QACzE,IAAA,0BAAM,EAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,uCAAe,CAAC,OAAO,CAAC,CAAC;IACvF,CAAC;IAEM,UAAU,CAAC,IAAc;QAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAEvC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAc,EAAE,EAAE;YACxD,MAAM,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAE,CAAC;YAC3C,uBAAuB;YACvB,IAAI,CAAC,CAAC,EAAE,CAAC;gBACL,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACtD,OAAO,KAAK,CAAC;YACjB,CAAC;YACD,OAAO,CAAC,CAAC,UAAW,CAAC,IAAK,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,UAAW,CAAC,IAAK,CAAC,QAAQ,EAAE,CAAC;QAChF,CAAC,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,IAAA,0BAAM,EAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,qBAAqB,CAAC,CAAC;YACtD,6DAA6D;YAC7D,qDAAqD;YACrD,OAAO,KAAK,CAAC,CAAC,SAAS;QAC3B,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEzC,QAAQ,aAAa,EAAE,CAAC;YACpB,KAAK,IAAI,CAAC;YACV,KAAK,SAAS;gBACV,uBAAuB;gBACvB,OAAO;oBACH,QAAQ,CACJ,OAAO,EACP,IAAA,yCAAS,EAAC,IAAI,CAAC,EACf,wBAAwB,EACxB,IAAI,CAAC,CAAC,CAAC,IAAA,yCAAS,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAC9B,CAAC;gBACN;;;;;;;;mBAQG;gBACH,OAAO,KAAK,CAAC;YAEjB,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,CAAC,QAAQ;YACzB,KAAK,UAAU;gBACX,iCAAiC;gBACjC,OAAO,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,UAAW,CAAC,IAAK,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YACtF,KAAK,qBAAqB;gBACtB,OAAO,KAAK,CAAC,CAAC,UAAU;YAC5B;gBACI,OAAO,KAAK,CAAC,CAAC,UAAU;QAChC,CAAC;IACL,CAAC;IAEM,SAAS,CAAC,aAA+C;QAC5D,MAAM,UAAU,GAAW,aAAa,CAAC,UAAU,CAAC,IAAK,CAAC;QAE1D,IAAI,GAAG,GAAgB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAClC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAQ,CAAC;QAC/C,CAAC;QACD,uBAAuB;QACvB,OAAO,IAAI,QAAQ,CAAC,YAAY,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QACnD,MAAM,SAAS,GAAG,IAAI,uCAAuC,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QACzF,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ;AApFD,0FAoFC"}