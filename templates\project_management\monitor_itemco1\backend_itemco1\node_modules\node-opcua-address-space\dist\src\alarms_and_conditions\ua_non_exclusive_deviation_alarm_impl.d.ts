/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
import { DataValue } from "node-opcua-data-value";
import { NodeId } from "node-opcua-nodeid";
import { DataType, VariantOptions } from "node-opcua-variant";
import { UAVariableT } from "node-opcua-address-space-base";
import { NamespacePrivate } from "../namespace_private";
import { AddressSpace } from "../address_space";
import { InstantiateLimitAlarmOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_limit_alarm_options";
import { UANonExclusiveDeviationAlarmEx } from "../../source/interfaces/alarms_and_conditions/ua_non_exclusive_deviation_alarm_ex";
import { UANonExclusiveLimitAlarmImpl } from "./ua_non_exclusive_limit_alarm_impl";
export declare interface UANonExclusiveDeviationAlarmImpl extends UANonExclusiveLimitAlarmImpl, UANonExclusiveDeviationAlarmEx {
    on(eventName: string, eventHandler: any): this;
    once(eventName: string, eventHandler: any): this;
    get addressSpace(): AddressSpace;
}
export declare class UANonExclusiveDeviationAlarmImpl extends UANonExclusiveLimitAlarmImpl implements UANonExclusiveDeviationAlarmEx {
    static instantiate(namespace: NamespacePrivate, type: string | NodeId, options: InstantiateLimitAlarmOptions, data?: Record<string, VariantOptions>): UANonExclusiveDeviationAlarmImpl;
    _setStateBasedOnInputValue(value: number): void;
    getSetpointNodeNode(): UAVariableT<number, DataType.Double> | UAVariableT<number, DataType.Float> | undefined;
    getSetpointValue(): number | null;
    _onSetpointDataValueChange(dataValue: DataValue): void;
    _install_setpoint(options: any): any;
}
