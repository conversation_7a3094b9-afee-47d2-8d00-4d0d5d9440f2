import { LocalizedText, LocalizedTextLike, QualifiedNameLike } from "node-opcua-data-model";
import { DataType } from "node-opcua-variant";
import { INamespace, UAVariable, UAProperty, ISessionContext } from "node-opcua-address-space-base";
import { DataValueT } from "node-opcua-data-value";
import { NumericRange } from "node-opcua-numeric-range";
import { UAVariableImpl } from "../ua_variable_impl";
import { AddTwoStateDiscreteOptions } from "../../source/address_space_ts";
import { UATwoStateDiscreteEx } from "../../source/interfaces/data_access/ua_two_state_discrete_ex";
import { ISetStateOptions } from "../../source/interfaces/i_set_state_options";
export interface UATwoStateDiscreteImpl {
    falseState: UAProperty<LocalizedText, /*c*/ DataType.LocalizedText>;
    trueState: UAProperty<LocalizedText, /*c*/ DataType.LocalizedText>;
    readValue(context?: ISessionContext | null, indexRange?: NumericRange, dataEncoding?: QualifiedNameLike | null): DataValueT<boolean, DataType.Boolean>;
    readValueAsync(context: ISessionContext | null, callback?: any): any;
}
export declare class UATwoStateDiscreteImpl extends UAVariableImpl implements UATwoStateDiscreteEx {
    _post_initialize(): void;
    setValue(value: boolean | LocalizedTextLike, options?: ISetStateOptions): void;
    getValue(): boolean;
    getValueAsString(): string;
    getTrueStateAsString(): string;
    getFalseStateAsString(): string;
    clone(options1: any, optionalFilter: any, extraInfo: any): UAVariable;
}
export declare function promoteToTwoStateDiscrete(node: UAVariable): UATwoStateDiscreteEx;
export declare function _addTwoStateDiscrete(namespace: INamespace, options: AddTwoStateDiscreteOptions): UATwoStateDiscreteEx;
