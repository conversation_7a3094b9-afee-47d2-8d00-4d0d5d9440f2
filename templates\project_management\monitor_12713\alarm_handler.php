<?php
/**
 * Alarm Handler for Project 12713
 * Handles saving and loading alarm data to/from file
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$project_name = '12713';
$alarm_file = __DIR__ . "/alarm_{$project_name}.json";

// Ensure directory exists
if (!file_exists(dirname($alarm_file))) {
    mkdir(dirname($alarm_file), 0755, true);
}

function loadAlarms($file_path) {
    if (!file_exists($file_path)) {
        return [];
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        return [];
    }
    
    $data = json_decode($content, true);
    return $data ?: [];
}

function saveAlarms($file_path, $alarms) {
    // Keep only last 1000 alarms to prevent file from getting too large
    if (count($alarms) > 1000) {
        $alarms = array_slice($alarms, -1000);
    }
    
    $json = json_encode($alarms, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents($file_path, $json) !== false;
}

function addAlarm($file_path, $alarm_data) {
    $alarms = loadAlarms($file_path);
    
    // Add timestamp if not provided
    if (!isset($alarm_data['timestamp'])) {
        $alarm_data['timestamp'] = date('Y-m-d H:i:s');
    }
    
    // Add unique ID
    $alarm_data['id'] = uniqid();
    
    // Add to beginning of array (newest first)
    array_unshift($alarms, $alarm_data);
    
    return saveAlarms($file_path, $alarms);
}

// Handle different actions
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'load':
        // Load all alarms
        $alarms = loadAlarms($alarm_file);
        
        // Apply filters if provided
        $from_date = $_GET['from_date'] ?? '';
        $to_date = $_GET['to_date'] ?? '';
        $name_filter = $_GET['name_filter'] ?? '';
        
        if ($from_date || $to_date || $name_filter) {
            $filtered_alarms = [];
            
            foreach ($alarms as $alarm) {
                $include = true;
                
                // Date filter
                if ($from_date && strtotime($alarm['timestamp']) < strtotime($from_date)) {
                    $include = false;
                }
                if ($to_date && strtotime($alarm['timestamp']) > strtotime($to_date)) {
                    $include = false;
                }
                
                // Name filter
                if ($name_filter && stripos($alarm['name'], $name_filter) === false) {
                    $include = false;
                }
                
                if ($include) {
                    $filtered_alarms[] = $alarm;
                }
            }
            
            $alarms = $filtered_alarms;
        }
        
        echo json_encode([
            'success' => true,
            'data' => $alarms,
            'total' => count($alarms)
        ]);
        break;
        
    case 'add':
        // Add new alarm
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['name'])) {
            echo json_encode(['success' => false, 'error' => 'Invalid alarm data']);
            exit;
        }
        
        $alarm_data = [
            'name' => $input['name'],
            'value' => $input['value'] ?? '',
            'description' => $input['description'] ?? '',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $success = addAlarm($alarm_file, $alarm_data);
        
        echo json_encode([
            'success' => $success,
            'message' => $success ? 'Alarm added successfully' : 'Failed to add alarm'
        ]);
        break;
        
    case 'stats':
        // Get alarm statistics
        $alarms = loadAlarms($alarm_file);
        $today = date('Y-m-d');
        
        $stats = [
            'total' => count($alarms),
            'today' => 0,
            'active' => 0
        ];
        
        foreach ($alarms as $alarm) {
            // Count today's alarms
            if (strpos($alarm['timestamp'], $today) === 0) {
                $stats['today']++;
            }
            
            // Count active alarms (value = ON)
            if (strtoupper($alarm['value']) === 'ON') {
                $stats['active']++;
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);
        break;
        
    case 'clear':
        // Clear all alarms (admin only)
        $success = saveAlarms($alarm_file, []);
        echo json_encode([
            'success' => $success,
            'message' => $success ? 'All alarms cleared' : 'Failed to clear alarms'
        ]);
        break;
        
    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}
?>
