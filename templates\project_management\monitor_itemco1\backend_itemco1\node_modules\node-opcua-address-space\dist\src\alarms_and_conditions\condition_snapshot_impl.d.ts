/**
 * @module node-opcua-address-space.AlarmsAndConditions
 */
import { EventEmitter } from "events";
import { IEventData, BaseNode } from "node-opcua-address-space-base";
import { UInt16 } from "node-opcua-basic-types";
import { LocalizedText, LocalizedTextLike } from "node-opcua-data-model";
import { NodeId } from "node-opcua-nodeid";
import { StatusCode } from "node-opcua-status-code";
import { TimeZoneDataType } from "node-opcua-types";
import { ConditionSnapshot } from "../../source/interfaces/alarms_and_conditions/condition_snapshot";
import { IConditionVariableTypeSetterOptions } from "../../source/interfaces/i_condition_variable_type_setter_options";
import { UtcTime } from "../../source/interfaces/state_machine/ua_state_machine_type";
import { ISetStateOptions } from "../../source/interfaces/i_set_state_options";
declare function normalizeName(str: string): string;
export declare class ConditionSnapshotImpl extends EventEmitter implements ConditionSnapshot {
    #private;
    static normalizeName: typeof normalizeName;
    condition: BaseNode;
    eventData: IEventData | null;
    branchId: NodeId | null;
    private _map;
    private _node_index;
    /**
     */
    constructor(condition: BaseNode, branchId: NodeId);
    _constructEventData(): IEventData;
    /**
     *
     */
    getBranchId(): NodeId;
    /**
     *
     */
    getEventId(): Buffer;
    /**
     *
     */
    getRetain(): boolean;
    /**
     *
     */
    setRetain(retainFlag: boolean): void;
    /**
     *
     */
    renewEventId(): void;
    /**
     *
     */
    getEnabledState(): boolean;
    /**
     *
     */
    setEnabledState(value: boolean, options?: ISetStateOptions): void;
    /**
     *
     */
    getEnabledStateAsString(): string;
    /**
     *
     */
    getComment(): LocalizedText;
    /**
     * Set condition comment
     *
     * Comment contains the last comment provided for a certain state (ConditionBranch). It may
     * have been provided by an AddComment Method, some other Method or in some other
     * manner. The initial value of this Variable is null, unless it is provided in some other manner. If
     * a Method provides as an option the ability to set a Comment, then the value of this Variable is
     * reset to null if an optional comment is not provided.
     *
     */
    setComment(txtMessage: LocalizedTextLike, options?: IConditionVariableTypeSetterOptions): void;
    /**
     * set the condition message (localized text)
     */
    setMessage(txtMessage: LocalizedTextLike | LocalizedText): void;
    /**
     *
     */
    setClientUserId(userIdentity: string): void;
    /**
     * set the condition quality
     *
     *
     * as per spec 1.0.3 - Part 9
     *
     * Quality reveals the status of process values or other resources that this Condition
     * instance is based upon.
     *
     * If, for example, a process value is “Uncertain”, the associated “LevelAlarm”
     * Condition is also questionable.
     *
     * Values for the Quality can be any of the OPC StatusCodes defined in Part 8
     * as well as Good, Uncertain and Bad as defined in Part 4.
     *
     * These  StatusCodes are similar to but slightly more generic than the description
     * of data quality in the various field bus specifications.
     *
     * It is the responsibility of the Server to map internal status information to these codes.
     *
     * A Server which supports no quality information shall return Good.
     *
     * This quality can also reflect the communication status associated with the system that this
     * value or resource is based on and from which this Alarm was received. For communication
     * errors to the underlying system, especially those that result in some unavailable
     * Event fields, the quality shall be BadNoCommunication error.
     *
     * Quality refers to the quality of the data value(s) upon which this Condition is based.
     *
     * Since a Condition is usually based on one or more Variables, the Condition inherits
     *  the quality of these Variables. E.g., if the process value is “Uncertain”,
     *  the “LevelAlarm” Condition is also questionable.
     *
     * If more than one variable is represented by a given condition or if the condition
     * is from an underlining system and no direct mapping to a variable is available,
     * it is up to the application to determine what quality is displayed as part of the condition.
     */
    setQuality(quality: StatusCode, options?: IConditionVariableTypeSetterOptions): void;
    /**
     *
     */
    getQuality(): StatusCode;
    /**
     * **as per spec 1.0.3 - Part 9**
     *
     * The Severity of a Condition is inherited from the base Event model defined in Part 5. It
     * indicates the urgency of the Condition and is also commonly called ‘priority’, especially in
     * relation to Alarms in the ProcessConditionClass.
     *
     * **as per spec 1.0.3 - Part 5**
     *
     * Severity is an indication of the urgency of the Event. This is also commonly
     * called “priority”.
     *
     * Values will range from 1 to 1 000, with 1 being the lowest severity and 1 000
     * being the highest.
     *
     * Typically, a severity of 1 would indicate an Event which is informational in nature,
     * while a value of 1 000 would indicate an Event of catastrophic nature, which could
     * potentially result in severe financial loss or loss of life.
     *
     * It is expected that very few Server implementations will support 1 000 distinct
     * severity levels.
     *
     * Therefore, Server developers are responsible for distributing their severity levels
     * across the  1 to 1 000 range in such a manner that clients can assume a linear
     * distribution.
     *
     *
     * For example, a  client wishing to present five severity levels to a user should be
     * able to do the following mapping:
     *
     *
     *            Client Severity OPC Severity
     *                HIGH        801 – 1 000
     *                MEDIUM HIGH 601 – 800
     *                MEDIUM      401 – 600
     *                MEDIUM LOW  201 – 400
     *                LOW           1 – 200
     *
     * In many cases a strict linear mapping of underlying source severities to the OPC
     * Severity range is not appropriate. The Server developer will instead intelligently
     * map the underlying source severities to the 1 to 1 000 OPC Severity range in some
     * other fashion.
     *
     * In particular, it it recommended that Server developers map Events of high urgency
     * into the OPC severity range of 667 to 1 000, Events of medium urgency into the
     * OPC severity range of 334 to 666 and Events of low urgency into OPC severities
     * of 1 to 333.
     */
    setSeverity(severity: UInt16, options?: IConditionVariableTypeSetterOptions): void;
    /**
     */
    getSeverity(): UInt16;
    getSeveritySourceTimestamp(): Date;
    /**
     *  LastSeverity provides the previous severity of the ConditionBranch.
     *
     * **as per spec 1.0.3 - part 9**
     *
     *
     *  Initially this Variable  contains a zero value;
     *  it will return a value only after a severity change. The new severity is
     *  supplied via the Severity Property which is inherited from the BaseEventType.
     *
     */
    setLastSeverity(severity: UInt16, options?: IConditionVariableTypeSetterOptions): void;
    /**
     *
     */
    getLastSeverity(): UInt16;
    /**
     * setReceiveTime
     *
     * **as per OPCUA 1.0.3 part 5**:
     *
     * ReceiveTime provides the time the OPC UA Server received the Event from the underlying
     * device of another Server.
     *
     * ReceiveTime is analogous to ServerTimestamp defined in Part 4, i.e.
     * in the case where the OPC UA Server gets an Event from another OPC UA Server, each Server
     * applies its own ReceiveTime. That implies that a Client may get the same Event, having the
     * same EventId, from different Servers having different values of the ReceiveTime.
     *
     * The ReceiveTime shall always be returned as value and the Server is not allowed to return a
     * StatusCode for the ReceiveTime indicating an error.
     *
     */
    setReceiveTime(time: UtcTime): void;
    /**
     * Time provides the time the Event occurred.
     *
     * **as per OPCUA 1.0.3 part 5**:
     *
     * This value is set as close to the event generator as
     * possible. It often comes from the underlying system or device.
     *
     * Once set, intermediate OPC UA Servers shall not alter the value.
     *
     */
    setTime(time: Date): void;
    /**
     * LocalTime is a structure containing the Offset and the DaylightSavingInOffset flag.
     *
     * The Offset specifies the time difference (in minutes) between the Time Property
     * and the time at the location in which the event was issued.
     *
     *
     * If DaylightSavingInOffset is TRUE, then Standard/Daylight savings time (DST) at
     * the originating location is in effect and Offset includes the DST correction.
     *
     * If `false` then the Offset does not include DST correction and DST may or may not have been
     * in effect.
     *
     */
    setLocalTime(localTime: TimeZoneDataType): void;
    getSourceName(): LocalizedText;
    /**
     *
     */
    getSourceNode(): NodeId;
    /**
     *
     */
    getEventType(): NodeId;
    getMessage(): LocalizedText;
    isCurrentBranch(): boolean;
    getAckedState(): boolean;
    setAckedState(ackedState: boolean, options?: ISetStateOptions): StatusCode;
    getConfirmedState(): boolean;
    setConfirmedStateIfExists(confirmedState: boolean, options?: ISetStateOptions): void;
    setConfirmedState(confirmedState: boolean): void;
    getSuppressedState(): boolean;
    setSuppressedState(suppressed: boolean, options?: ISetStateOptions): void;
    getActiveState(): boolean;
    setActiveState(newActiveState: boolean, options?: ISetStateOptions): StatusCode;
    setLatchedState(newLatchedState: boolean, options?: ISetStateOptions): StatusCode;
    getLatchedState(): boolean;
    setOutOfServiceState(newOutOfServiceState: boolean, options?: ISetStateOptions): StatusCode;
    getOutOfServiceState(): boolean;
    setSilentState(newSilentState: boolean, options?: ISetStateOptions): StatusCode;
    getSilentState(): boolean;
    setShelvingState(): void;
    toString(): string;
    _setAckedState(requestedAckedState: boolean, conditionEventId?: Buffer, comment?: string | LocalizedText | LocalizedTextLike, options?: ISetStateOptions): StatusCode;
}
export {};
