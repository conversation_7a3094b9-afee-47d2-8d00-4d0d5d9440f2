import { NodeId } from "node-opcua-nodeid";
import { VariantOptions } from "node-opcua-variant";
import { UAEventType } from "node-opcua-address-space-base";
import { NamespacePrivate } from "../namespace_private";
import { UAExclusiveLimitAlarmEx } from "../../source/interfaces/alarms_and_conditions/ua_exclusive_limit_alarm_ex";
import { InstantiateLimitAlarmOptions } from "../../source/interfaces/alarms_and_conditions/instantiate_limit_alarm_options";
import { UALimitAlarmImpl } from "./ua_limit_alarm_impl";
export declare interface UAExclusiveLimitAlarmImpl extends UAExclusiveLimitAlarmEx {
}
export declare class UAExclusiveLimitAlarmImpl extends UALimitAlarmImpl implements UAExclusiveLimitAlarmEx {
    /***
     *
     * @param namespace {INamespace}
     * @param type
     * @param options
     * @param data
     * @return {UAExclusiveLimitAlarm}
     */
    static instantiate(namespace: NamespacePrivate, type: UAEventType | string | NodeId, options: InstantiateLimitAlarmOptions, data?: Record<string, VariantOptions>): UAExclusiveLimitAlarmImpl;
    _signalNewCondition(stateName: string | null, isActive: boolean, value: string): void;
    _setStateBasedOnInputValue(value: number): void;
}
