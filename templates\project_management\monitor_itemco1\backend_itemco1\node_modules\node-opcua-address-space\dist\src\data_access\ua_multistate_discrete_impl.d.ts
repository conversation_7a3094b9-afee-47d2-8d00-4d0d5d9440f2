import { LocalizedText } from "node-opcua-data-model";
import { StatusCode } from "node-opcua-status-code";
import { DataType, Variant } from "node-opcua-variant";
import { INamespace, UAVariable, UAProperty } from "node-opcua-address-space-base";
import { UAMultiStateDiscrete_Base } from "node-opcua-nodeset-ua";
import { UAVariableImpl } from "../ua_variable_impl";
import { ISetStateOptions } from "../../source/interfaces/i_set_state_options";
import { AddMultiStateDiscreteOptions } from "../../source/address_space_ts";
export { UAMultiStateDiscrete } from "node-opcua-nodeset-ua";
export interface UAMultiStateDiscreteEx<T, DT extends DataType> extends UAMultiStateDiscrete_Base<T, DT> {
    getValue(): number;
    getValueAsString(): string;
    getIndex(value: string): number;
    setValue(value: string | number, options?: ISetStateOptions): void;
    checkVariantCompatibility(value: Variant): StatusCode;
}
export interface UAMultiStateDiscreteImpl<T, DT extends DataType> {
    enumStrings: UAProperty<LocalizedText[], DataType.LocalizedText>;
}
/**
 * @class UAMultiStateDiscrete
 */
export declare class UAMultiStateDiscreteImpl<T, DT extends DataType> extends UAVariableImpl implements UAMultiStateDiscreteEx<T, DT> {
    getValue(): number;
    getValueAsString(): string;
    getIndex(value: string): number;
    setValue(value: string | number, options?: ISetStateOptions): void;
    checkVariantCompatibility(value: Variant): StatusCode;
    _post_initialize(): void;
    clone<T, DT extends DataType>(options1: any, optionalFilter: any, extraInfo: any): UAMultiStateDiscreteImpl<T, DT>;
}
export declare function promoteToMultiStateDiscrete<T, DT extends DataType>(node: UAVariable): UAMultiStateDiscreteImpl<T, DT>;
export declare function _addMultiStateDiscrete<T, DT extends DataType>(namespace: INamespace, options: AddMultiStateDiscreteOptions): UAMultiStateDiscreteImpl<T, DT>;
