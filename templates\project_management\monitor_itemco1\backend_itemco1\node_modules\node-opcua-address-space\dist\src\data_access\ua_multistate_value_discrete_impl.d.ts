import { DataType, Variant } from "node-opcua-variant";
import { Int32, Int64 } from "node-opcua-basic-types";
import { LocalizedText, QualifiedNameLike } from "node-opcua-data-model";
import { DataValueT } from "node-opcua-data-value";
import { StatusCode } from "node-opcua-status-code";
import { NumericRange } from "node-opcua-numeric-range";
import { DTEnumValue } from "node-opcua-nodeset-ua";
import { INamespace, UAVariable, UAProperty, ISessionContext } from "node-opcua-address-space-base";
import { UAMultiStateValueDiscreteEx } from "../../source/interfaces/data_access/ua_multistate_value_discrete_ex";
import { AddMultiStateValueDiscreteOptions } from "../../source/address_space_ts";
import { ISetStateOptions } from "../../source/interfaces/i_set_state_options";
import { UAVariableImpl } from "../ua_variable_impl";
export interface UAMultiStateValueDiscreteImpl<T, DT extends DataType> {
    enumValues: UAProperty<DTEnumValue[], DataType.ExtensionObject>;
    valueAsText: UAProperty<LocalizedText, DataType.LocalizedText>;
    readValue(context?: ISessionContext | null, indexRange?: NumericRange, dataEncoding?: QualifiedNameLike | null): DataValueT<T, DT>;
    readValueAsync(context: ISessionContext | null, callback?: any): any;
}
export declare class UAMultiStateValueDiscreteImpl<T, DT extends DataType> extends UAVariableImpl implements UAMultiStateValueDiscreteEx<T, DT> {
    setValue(value: string | number | Int64, options?: ISetStateOptions): void;
    getValueAsString(): any;
    getValueAsNumber(): any;
    checkVariantCompatibility(value: Variant): StatusCode;
    clone<T, DT extends DataType>(options1: any, optionalFilter: any, extraInfo: any): UAMultiStateValueDiscreteImpl<T, DT>;
    /**
     * @private
     */
    _isValueInRange(value: number): boolean;
    /**
     *
     * @private
     */
    _enumValueIndex(): Record<Int32, DTEnumValue>;
    /**
     *
     * @private
     */
    _setValue(value: Int64, options?: ISetStateOptions): void;
    /**
     *
     * @private
     */
    findValueAsText(value?: number | Int64): Variant;
    _getDataType(): DataType;
    /**
     *
     * @private
     */
    _post_initialize(): void;
}
export declare function promoteToMultiStateValueDiscrete<T, DT extends DataType>(node: UAVariable): UAMultiStateValueDiscreteImpl<T, DT>;
export declare function _addMultiStateValueDiscrete<T, DT extends DataType>(namespace: INamespace, options: AddMultiStateValueDiscreteOptions): UAMultiStateValueDiscreteEx<T, DT>;
export declare function validateIsNumericDataType(dataTypeValue: any): void;
/** @deprecated: use validateIsNumericDataType instead */
export declare const validateDataType: typeof validateIsNumericDataType;
